﻿using Amazon;
using AutoMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using platform.Models.Request;
using platform.Models.Response;
using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.MessageBus.Models;
using shared.Models.Documents.DynamoDB;
using shared.Models.Documents.DynamoDB.ProviderData;
using shared.Models.Response;
using shared.Models.Enums;
using static shared.Components.InternalProcess.Examples.BasicUsageExample;
using System.ComponentModel;

namespace platform.Controllers
{

    public partial class KnowledgeBaseController
    {

        #region CREATION PROCESS
        private InternalProcessStateMachine<KnowledgeBaseStatus, shared.Models.Documents.DynamoDB.KnowledgeBase> GetCreationProcessSM(){
            var stateMachine = new InternalProcessStateMachine<KnowledgeBaseStatus, shared.Models.Documents.DynamoDB.KnowledgeBase>();

            // Configure states with their tasks
            stateMachine
                .ConfigureState(KnowledgeBaseStatus.QUEUED, ValidateDocumentTask, "Validate document format and content")
                .ConfigureState(KnowledgeBaseStatus.DB_ENTRY_CREATED, ProcessDocumentTask, "Process the document content")
                .ConfigureState(KnowledgeBaseStatus.KB_CREATED, ProcessDocumentTask, "Process the document content")
                .ConfigureState(KnowledgeBaseStatus.DATASOURCE_CREATED, ProcessDocumentTask, "Process the document content")
                .ConfigureState(KnowledgeBaseStatus.READY, CompleteDocumentTask, "Finalize document processing");

            // Configure transitions
            stateMachine
                .AddTransition(KnowledgeBaseStatus.QUEUED, InternalProcessState.Succeeded, KnowledgeBaseStatus.DB_ENTRY_CREATED)
                .AddTransition(KnowledgeBaseStatus.DB_ENTRY_CREATED, InternalProcessState.Succeeded, KnowledgeBaseStatus.KB_CREATED)
                .AddTransition(KnowledgeBaseStatus.DB_ENTRY_CREATED, InternalProcessState.Failed, KnowledgeBaseStatus.QUEUED)
                .AddTransition(KnowledgeBaseStatus.KB_CREATED, InternalProcessState.Succeeded, KnowledgeBaseStatus.DATASOURCE_CREATED)
                .AddTransition(KnowledgeBaseStatus.KB_CREATED, InternalProcessState.Failed, KnowledgeBaseStatus.DB_ENTRY_CREATED)
                .AddTransition(KnowledgeBaseStatus.DATASOURCE_CREATED, InternalProcessState.Succeeded, KnowledgeBaseStatus.READY)
                .AddTransition(KnowledgeBaseStatus.DATASOURCE_CREATED, InternalProcessState.Failed, KnowledgeBaseStatus.KB_CREATED);

            // Add final transitions to mark the process as finished
            stateMachine
                .AddFinalTransition(KnowledgeBaseStatus.READY, InternalProcessState.Succeeded, "Document processing completed successfully")
                .AddFinalTransition(KnowledgeBaseStatus.QUEUED, InternalProcessState.Failed, "Document processing failed");

            var validationErrors = stateMachine.ValidateConfiguration();

            if (validationErrors.Any())
            {
                Console.WriteLine("Configuration errors:");
                foreach (var error in validationErrors)
                {
                    Console.WriteLine($"  - {error}");
                }
                throw new Exception("State Machine Failed to create due to validation exceptions.");
            }

            return stateMachine;
        }
        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateRequest([FromBody] Models.Request.KnowledgeBaseCreateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            string accountId = GetAccountId();
            string kbId = Guid.NewGuid().ToString();

            AwsKnowledgeBase knowledgeBase = new AwsKnowledgeBase();
            knowledgeBase.Name = request.Name;
            knowledgeBase.Description = request.Description;
            knowledgeBase.AccountId = accountId;
            knowledgeBase.KbId = kbId;
            knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.QUEUED;
            knowledgeBase.LastChangeTimestamp = CurrentTimestamp();
            knowledgeBase.AwsData = new AwsKnowledgeBaseData();

            var sm = GetCreationProcessSM();



            return Ok(request);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.KnowledgeBaseController.Internal.CREATION_PROCESS)]
        [HttpPost]
        public async Task<IActionResult> CreateProcess(MessageBusMessage message)
        {
            
        }
        #endregion








        private async Task<AwsKnowledgeBase> CreateNewKb_CreateKb(AwsKnowledgeBase knowledgeBase)
        {

            var createKnowledgeBaseRequest = new Amazon.BedrockAgent.Model.CreateKnowledgeBaseRequest();
            createKnowledgeBaseRequest.ClientToken = knowledgeBase.KbId;
            createKnowledgeBaseRequest.Description = knowledgeBase.Description;
            createKnowledgeBaseRequest.Name = knowledgeBase.KbId;

            if (createKnowledgeBaseRequest.Description.Length == 0) createKnowledgeBaseRequest.Description = "no description";

            createKnowledgeBaseRequest.RoleArn = $"arn:aws:iam::{await iIAM.GetProviderAccountNumber()}:role/service-role/{kbConfiguration.CurrentValue.KnowledgeBaseRoleName}";

            createKnowledgeBaseRequest.KnowledgeBaseConfiguration = new Amazon.BedrockAgent.Model.KnowledgeBaseConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.Type = KnowledgeBaseType.VECTOR;
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration = new Amazon.BedrockAgent.Model.VectorKnowledgeBaseConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelArn = kbConfiguration.CurrentValue.DefaultEmbeddingsModelArn;
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration = new Amazon.BedrockAgent.Model.EmbeddingModelConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration.BedrockEmbeddingModelConfiguration = new Amazon.BedrockAgent.Model.BedrockEmbeddingModelConfiguration();
            createKnowledgeBaseRequest.KnowledgeBaseConfiguration.VectorKnowledgeBaseConfiguration.EmbeddingModelConfiguration.BedrockEmbeddingModelConfiguration.Dimensions = 1024;

            createKnowledgeBaseRequest.StorageConfiguration = new Amazon.BedrockAgent.Model.StorageConfiguration();
            createKnowledgeBaseRequest.StorageConfiguration.Type = KnowledgeBaseStorageType.PINECONE;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration = new Amazon.BedrockAgent.Model.PineconeConfiguration();
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.Namespace = knowledgeBase.KbId;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.ConnectionString = kbConfiguration.CurrentValue.VectorStorePath;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.CredentialsSecretArn = kbConfiguration.CurrentValue.VectorStoreSecretArn;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping = new Amazon.BedrockAgent.Model.PineconeFieldMapping();
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping.MetadataField = kbConfiguration.CurrentValue.PineconeConfiguration.Metadata;
            createKnowledgeBaseRequest.StorageConfiguration.PineconeConfiguration.FieldMapping.TextField = kbConfiguration.CurrentValue.PineconeConfiguration.Text;

            try
            {
                var resp = await bedrockAgent.CreateKnowledgeBaseAsync(createKnowledgeBaseRequest);

                knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.KB_CREATED;

                if (knowledgeBase.AwsData == null) throw new Exception("knowledgebase provider data was null");
                knowledgeBase.AwsData.AwsKbId = resp.KnowledgeBase.KnowledgeBaseId;

                return knowledgeBase;
            }
            catch (Exception ex)
            {
                return knowledgeBase;
            }
        }

        private async Task<AwsKnowledgeBase> Create_CreateDatasource(AwsKnowledgeBase knowledgeBase)
        {
            var awsRequest = new Amazon.BedrockAgent.Model.CreateDataSourceRequest();

            awsRequest.Name = knowledgeBase.KbId + "-ds";
            awsRequest.Description = knowledgeBase.Description;
            if (knowledgeBase.AwsData == null) throw new Exception("knjowledgebase provider data was null");
            awsRequest.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;

            awsRequest.VectorIngestionConfiguration = new Amazon.BedrockAgent.Model.VectorIngestionConfiguration();

            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration = new Amazon.BedrockAgent.Model.ChunkingConfiguration();
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.ChunkingStrategy = ChunkingStrategy.FIXED_SIZE;
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration = new Amazon.BedrockAgent.Model.FixedSizeChunkingConfiguration();
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration.MaxTokens = 300;
            awsRequest.VectorIngestionConfiguration.ChunkingConfiguration.FixedSizeChunkingConfiguration.OverlapPercentage = 20;

            awsRequest.DataSourceConfiguration = new Amazon.BedrockAgent.Model.DataSourceConfiguration();
            awsRequest.DataSourceConfiguration.Type = DataSourceType.S3;

            awsRequest.DataSourceConfiguration.S3Configuration = new Amazon.BedrockAgent.Model.S3DataSourceConfiguration();
            awsRequest.DataSourceConfiguration.S3Configuration.BucketArn = kbConfiguration.CurrentValue.KnowledgeBaseBucketArn;
            awsRequest.DataSourceConfiguration.S3Configuration.InclusionPrefixes = new List<string> { $"kbs/AccountId={knowledgeBase.AccountId}/KbId={knowledgeBase.KbId}/" };
            awsRequest.ClientToken = knowledgeBase.KbId;

            var resp = await bedrockAgent.CreateDataSourceAsync(awsRequest);

            knowledgeBase.AwsData.ExternalDataSourcesIds.Add(resp.DataSource.DataSourceId);
            knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.DATASOURCE_CREATED;

            return knowledgeBase;
        }

        private async Task<bool> Create_CheckAWSKnowledgeBaseStatus(AwsKnowledgeBase knowledgeBase)
        {
            var request = new Amazon.BedrockAgent.Model.GetKnowledgeBaseRequest();
            if (knowledgeBase.AwsData == null) throw new Exception("knowledgebase provider data was null");
            request.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
            var response = await bedrockAgent.GetKnowledgeBaseAsync(request);
            return response.KnowledgeBase.Status == Amazon.BedrockAgent.KnowledgeBaseStatus.ACTIVE;
        }

        private async Task<bool> Create_CheckAWSDatasourceStatus(AwsKnowledgeBase knowledgeBase)
        {
            var request = new Amazon.BedrockAgent.Model.GetDataSourceRequest();
            if (knowledgeBase.AwsData == null) throw new Exception("knowledgebase provider data was null");
            request.KnowledgeBaseId = knowledgeBase.AwsData.AwsKbId;
            request.DataSourceId = knowledgeBase.AwsData.ExternalDataSourcesIds.Last();
            var response = await bedrockAgent.GetDataSourceAsync(request);
            return response.DataSource.Status == DataSourceStatus.AVAILABLE;
        }
 

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPost]
        public async Task<IActionResult> CreateRequest([FromBody] Models.Request.KnowledgeBaseCreateRequest request)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            string accountId = GetAccountId();
            string kbId = Guid.NewGuid().ToString();

            AwsKnowledgeBase knowledgeBase = new AwsKnowledgeBase();
            knowledgeBase.Name = request.Name;
            knowledgeBase.Description = request.Description;
            knowledgeBase.AccountId = accountId;
            knowledgeBase.KbId = kbId;
            knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.QUEUED;
            knowledgeBase.LastChangeTimestamp = CurrentTimestamp();
            knowledgeBase.AwsData = new AwsKnowledgeBaseData();

            await DispatchApiEventV2(
                knowledgeBase, 
                MicroserviceType.Platform, 
                Constants.Routes.KnowledgeBaseController.BasePath,
                Constants.Routes.KnowledgeBaseController.Internal.CREATION_PROCESS, 
                0);

            return Ok(request);
        }
    

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.MicroserviceAuthScheme)]
        [Route(Constants.Routes.KnowledgeBaseController.Internal.CREATION_PROCESS + "old")]
        [HttpPost]
        public async Task<IActionResult> CreateInternalRequest([FromBody] AwsKnowledgeBase knowledgeBase)
        {
            string accountId = GetAccountId();

            int requeueDelay = 10;
            switch (knowledgeBase.Status)
            {
                case shared.Models.Enums.KnowledgeBaseStatus.QUEUED:
                    if (await PutDBEntry(knowledgeBase) != null)
                    {
                        requeueDelay = 0;
                        knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.DB_ENTRY_CREATED;
                        await PutDBEntry(knowledgeBase);
                    }
                    else
                    {
                        requeueDelay = 5;
                    }
                    break;
                case shared.Models.Enums.KnowledgeBaseStatus.DB_ENTRY_CREATED:
                    knowledgeBase = await CreateNewKb_CreateKb(knowledgeBase);
                    requeueDelay = 5;
                    await PutDBEntry(knowledgeBase);
                    break;
                case shared.Models.Enums.KnowledgeBaseStatus.KB_CREATED:
                    if (await Create_CheckAWSKnowledgeBaseStatus(knowledgeBase))
                    {
                        knowledgeBase = await Create_CreateDatasource(knowledgeBase);
                        requeueDelay = 5;
                        await PutDBEntry(knowledgeBase);
                    }
                    else
                    {
                        requeueDelay = 10;
                    }
                    break;
                case shared.Models.Enums.KnowledgeBaseStatus.DATASOURCE_CREATED:
                    if (!await Create_CheckAWSDatasourceStatus(knowledgeBase))
                    {
                        requeueDelay = 15;
                    }
                    else
                    {
                        knowledgeBase.Status = shared.Models.Enums.KnowledgeBaseStatus.READY;
                        requeueDelay = 0;
                        await PutDBEntry(knowledgeBase);
                    }
                    break;
                case shared.Models.Enums.KnowledgeBaseStatus.READY:
                    var updatedKb = await GetDBEntry<AwsKnowledgeBase>(accountId, knowledgeBase.KbId);
                    if (updatedKb.Status == shared.Models.Enums.KnowledgeBaseStatus.READY)
                    {
                        logger.LogInformation($"KB {knowledgeBase.KbId} fully created for accountId={knowledgeBase.AccountId}");
                        return Ok();
                    }
                    requeueDelay = 5;
                    break;
            }
            
            await DispatchApiEvent(
                knowledgeBase, 
                MicroserviceType.Platform, 
                Constants.Routes.KnowledgeBaseController.BasePath, 
                Constants.Routes.KnowledgeBaseController.Internal.CREATION_PROCESS, 
                requeueDelay);
            return Ok();

        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet]
        public async Task<IActionResult> ListKnowledgeBases(int count = 0, string? nextToken = null)
        {
            if (count < 10) count = 10;
            if (count > 100) count = 100;

            
            var listEntriesInternal = await GetDBEntries<AwsKnowledgeBase>(nameof(AwsKnowledgeBase.AccountId), GetAccountId(), count, nextToken, getTotal: nextToken == null);

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsKnowledgeBase, KnowledgeBaseResponse>();
            }, loggerFactory).CreateMapper();


            var result = new ListResponse<KnowledgeBaseResponse>()
            {
                Entries = mapper.Map<IList<AwsKnowledgeBase>, IList<KnowledgeBaseResponse>>(listEntriesInternal.Entries).ToList(),
                NextToken = listEntriesInternal.NextToken,
                Total = listEntriesInternal.Total,
            };

            return Ok(result);
        }


        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpPut(Constants.Routes.KnowledgeBaseController.Public.PUT)]
        public async Task<IActionResult> PutKnowledgeBase(string kbId, [FromBody] AgentPutRequest knowledgeBasePutRequest) {
            string accountId = GetAccountId();
            KnowledgeBase kb = await GetDBEntry<KnowledgeBase>(accountId, kbId);
            if (kb == null) return BadRequest("Not found");

            kb.Name = knowledgeBasePutRequest.Name;
            kb.Description = knowledgeBasePutRequest.Description;

            if (await UpdateDBEntry<KnowledgeBase>(kb, new List<string>() { nameof(KnowledgeBase.Name), nameof(KnowledgeBase.Description) }) == null)
            {
                return StatusCode(500, "Couldn't update");
            }
            return Ok(kb);
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseController.Public.GET_KNOWLEDGEBASE)]
        public async Task<IActionResult> GetKnowledgeBase(string kbId)
        {
            string accountId = GetAccountId();
            AwsKnowledgeBase kb = await GetDBEntry<AwsKnowledgeBase>(accountId, kbId);
            if (kb == null) return BadRequest("Not found");

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<AwsKnowledgeBase, KnowledgeBaseResponse>();
            }, loggerFactory).CreateMapper();
            
            return Ok(mapper.Map<AwsKnowledgeBase, KnowledgeBaseResponse>(kb));
        }

        [Authorize(AuthenticationSchemes = shared.Constants.Authentication.UserAuthScheme)]
        [HttpGet(Constants.Routes.KnowledgeBaseController.Public.GET_AGENTS_FOR_KNOWLEDGE_BASE)]
        public async Task<IActionResult> GetAgentsForKnowledgeBase(string kbId, [FromQuery] string? nextToken = null, [FromQuery] int count = -1)
        {
            string accountId = GetAccountId();
            ListResponse<AgentKnowledgeBase> response = await GetDBEntries<AgentKnowledgeBase>(
                nameof(AgentKnowledgeBase.KnowledgebaseId),
                kbId,
                count,
                nextToken,
                AgentKnowledgeBase.KnowledgebaseIdHashIndex,
                filterExpression: new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(AgentKnowledgeBase.AccountId)}", nameof(AgentKnowledgeBase.AccountId) } },
                    ExpressionAttributeValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry> { { $":{nameof(AgentKnowledgeBase.AccountId)}", accountId } },
                    ExpressionStatement = $"#{nameof(AgentKnowledgeBase.AccountId)}=:{nameof(AgentKnowledgeBase.AccountId)}"
                },
                getTotal: false
            ); 

            if (response == null) return BadRequest("Not found");
            if (response.Entries.Count == 0)
            {
                return Ok(new ListResponse<AgentResponse>());
            }

            var attValues = new Dictionary<string, Amazon.DynamoDBv2.DocumentModel.DynamoDBEntry>();
            var statement = new List<string>();

            foreach (var item in response.Entries.Select((value, i) => new { i, value }))
            {
                if (item.value.AccountId != accountId) continue;
                attValues.Add($":kb{item.i}", item.value.AgentId);
                statement.Add($"#{nameof(Agent.AgentId)}=:kb{item.i}");
            }

            ListResponse<Agent> agents = await GetDBEntries<Agent>(
                nameof(Agent.AccountId),
                GetAccountId(),
                response.Entries.Count,
                filterExpression: new Amazon.DynamoDBv2.DocumentModel.Expression()
                {
                    ExpressionAttributeNames = new Dictionary<string, string>() { { $"#{nameof(Agent.AgentId)}", nameof(Agent.AgentId) } },
                    ExpressionAttributeValues = attValues,
                    ExpressionStatement = String.Join(" OR ", statement)
                },
                getTotal: false
            );

            var loggerFactory = HttpContext.RequestServices.GetRequiredService<ILoggerFactory>();
            var mapper = new MapperConfiguration(cfg =>
            {
                cfg.CreateMap<Agent, AgentResponse>();
            }, loggerFactory).CreateMapper();

            var finalResponse = new ListResponse<AgentResponse>()
            {
                Entries = mapper.Map<IList<Agent>, IList<AgentResponse>>(agents.Entries).ToList(),
                NextToken = agents.NextToken,
                Total = agents.Total,
            };

            return Ok(finalResponse);
        }



    }
}
        