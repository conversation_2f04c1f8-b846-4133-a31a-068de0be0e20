using System.Text.Json.Serialization;
using shared.Converters;

namespace shared.Components.InternalProcess.Enums
{
    /// <summary>
    /// Represents the internal state of a process state machine.
    /// These states control the flow and transitions within the state machine.
    /// </summary>
    [JsonConverter(typeof(JsonEnumStringConverter<InternalProcessState>))]
    public enum InternalProcessState
    {
        /// <summary>
        /// Indicates the state machine has completed successfully and should not continue processing.
        /// This is a terminal state.
        /// </summary>
        Finished,

        /// <summary>
        /// Indicates the current state task failed and the next state should handle failure scenarios.
        /// This allows for error handling and recovery workflows.
        /// </summary>
        Failed,

        /// <summary>
        /// Indicates the current state task succeeded and the next state should handle success scenarios.
        /// This allows for continuation of the normal workflow.
        /// </summary>
        Succeeded,

        /// <summary>
        /// Special state that indicates the current state should be retried.
        /// When this state is returned, the state machine doesn't transition but increases the retry counter
        /// and re-executes the current state task.
        /// </summary>
        Retry
    }
}
