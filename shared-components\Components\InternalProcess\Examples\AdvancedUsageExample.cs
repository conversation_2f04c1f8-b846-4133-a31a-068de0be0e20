using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Interfaces;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;
using System.Text.Json;

namespace shared.Components.InternalProcess.Examples
{
    /// <summary>
    /// Advanced example demonstrating retry logic, state persistence, and error handling.
    /// This example shows an order processing workflow with external API calls.
    /// </summary>
    public class AdvancedUsageExample
    {
        // Example enum for order processing states
        public enum OrderProcessingState
        {
            Created,
            PaymentPending,
            PaymentProcessing,
            InventoryCheck,
            Shipping,
            Completed,
            Cancelled,
            Failed
        }

        // Example order class that implements IStateful
        public class Order : IStateful<OrderProcessingState>
        {
            public string OrderId { get; set; } = Guid.NewGuid().ToString();
            public string CustomerId { get; set; } = string.Empty;
            public decimal Amount { get; set; }
            public OrderProcessingState Status { get; set; } = OrderProcessingState.Created;
            public string PaymentTransactionId { get; set; } = string.Empty;
            public List<string> Items { get; set; } = new List<string>();
            public List<string> ProcessingNotes { get; set; } = new List<string>();
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public DateTime? CompletedAt { get; set; }
        }

        /// <summary>
        /// Demonstrates advanced state machine usage with retry logic and persistence.
        /// </summary>
        public static async Task<bool> RunAdvancedExample()
        {
            // Create an order to process
            var order = new Order
            {
                CustomerId = "CUST123",
                Amount = 99.99m,
                Items = new List<string> { "Product A", "Product B" }
            };

            // Create the state machine with correlation ID
            var correlationId = $"ORDER_PROCESSING_{order.OrderId}";
            var stateMachine = new InternalProcessStateMachine<OrderProcessingState, Order>(order, correlationId);

            // Configure states with retry-capable tasks
            stateMachine
                .ConfigureState(OrderProcessingState.Created, InitiatePaymentTask, "Initiate payment processing", TimeSpan.FromSeconds(10))
                .ConfigureState(OrderProcessingState.PaymentPending, ProcessPaymentTask, "Process payment with external service", TimeSpan.FromSeconds(30))
                .ConfigureState(OrderProcessingState.PaymentProcessing, CheckInventoryTask, "Check inventory availability", TimeSpan.FromSeconds(15))
                .ConfigureState(OrderProcessingState.InventoryCheck, InitiateShippingTask, "Initiate shipping process", TimeSpan.FromSeconds(20))
                .ConfigureState(OrderProcessingState.Shipping, CompleteOrderTask, "Complete order processing", TimeSpan.FromSeconds(5));

            // Configure transitions including retry scenarios
            stateMachine
                .AddTransition(OrderProcessingState.Created, InternalProcessState.Succeeded, OrderProcessingState.PaymentPending)
                .AddTransition(OrderProcessingState.Created, InternalProcessState.Failed, OrderProcessingState.Failed)
                .AddTransition(OrderProcessingState.PaymentPending, InternalProcessState.Succeeded, OrderProcessingState.PaymentProcessing)
                .AddTransition(OrderProcessingState.PaymentPending, InternalProcessState.Failed, OrderProcessingState.Cancelled)
                .AddTransition(OrderProcessingState.PaymentProcessing, InternalProcessState.Succeeded, OrderProcessingState.InventoryCheck)
                .AddTransition(OrderProcessingState.PaymentProcessing, InternalProcessState.Failed, OrderProcessingState.Cancelled)
                .AddTransition(OrderProcessingState.InventoryCheck, InternalProcessState.Succeeded, OrderProcessingState.Shipping)
                .AddTransition(OrderProcessingState.InventoryCheck, InternalProcessState.Failed, OrderProcessingState.Cancelled)
                .AddTransition(OrderProcessingState.Shipping, InternalProcessState.Succeeded, OrderProcessingState.Completed)
                .AddTransition(OrderProcessingState.Shipping, InternalProcessState.Failed, OrderProcessingState.Failed);

            // Add final transitions
            stateMachine
                .AddFinalTransition(OrderProcessingState.Completed, InternalProcessState.Succeeded, "Order completed successfully")
                .AddFinalTransition(OrderProcessingState.Cancelled, InternalProcessState.Failed, "Order was cancelled")
                .AddFinalTransition(OrderProcessingState.Failed, InternalProcessState.Failed, "Order processing failed");

            // Demonstrate state persistence
            Console.WriteLine("=== Advanced Order Processing Example ===");
            Console.WriteLine($"Processing order {order.OrderId} for customer {order.CustomerId}");
            Console.WriteLine($"Order amount: ${order.Amount:F2}");

            // Execute step by step to demonstrate state persistence
            var iterationCount = 0;
            while (!stateMachine.IsFinished && iterationCount < 10)
            {
                iterationCount++;
                Console.WriteLine($"\n--- Iteration {iterationCount} ---");
                Console.WriteLine($"Current state: {stateMachine.CurrentState}");
                Console.WriteLine($"Internal state: {stateMachine.InternalState}");

                // Save state before execution (simulate persistence)
                var stateDataJson = JsonSerializer.Serialize(stateMachine.StateData, new JsonSerializerOptions { WriteIndented = true });
                Console.WriteLine("State data saved to persistence layer");

                // Execute one iteration
                var success = await stateMachine.ExecuteIteration();
                
                if (!success)
                {
                    Console.WriteLine($"Iteration failed. Last transition failed: {stateMachine.LastTransitionFailed}");
                    break;
                }

                Console.WriteLine($"New state: {stateMachine.CurrentState}");
                Console.WriteLine($"New internal state: {stateMachine.InternalState}");
                
                if (stateMachine.StateData.RetryCount > 0)
                {
                    Console.WriteLine($"Retry count: {stateMachine.StateData.RetryCount}");
                }
            }

            Console.WriteLine($"\n=== Final Results ===");
            Console.WriteLine($"Processing completed: {stateMachine.IsFinished}");
            Console.WriteLine($"Final order state: {order.Status}");
            Console.WriteLine($"Total iterations: {iterationCount}");
            
            if (order.ProcessingNotes.Any())
            {
                Console.WriteLine("Processing notes:");
                foreach (var note in order.ProcessingNotes)
                {
                    Console.WriteLine($"  - {note}");
                }
            }

            return stateMachine.IsFinished && order.Status == OrderProcessingState.Completed;
        }

        /// <summary>
        /// Task for initiating payment processing.
        /// </summary>
        private static async Task<InternalProcessState> InitiatePaymentTask(Order order, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Initiating payment for order {order.OrderId}...");
            order.ProcessingNotes.Add($"Payment initiation started at {DateTime.UtcNow}");

            // Simulate payment initiation
            await Task.Delay(100);

            order.PaymentTransactionId = Guid.NewGuid().ToString();
            Console.WriteLine($"Payment transaction created: {order.PaymentTransactionId}");

            return InternalProcessState.Succeeded;
        }

        /// <summary>
        /// Task for processing payment with retry logic.
        /// </summary>
        private static async Task<InternalProcessState> ProcessPaymentTask(Order order, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Processing payment for order {order.OrderId}...");
            order.ProcessingNotes.Add($"Payment processing attempt at {DateTime.UtcNow}");

            // Simulate external payment API call
            await Task.Delay(200);

            // Simulate occasional failures that should trigger retries
            var random = new Random();
            var failureChance = 0.3; // 30% chance of failure

            if (random.NextDouble() < failureChance)
            {
                Console.WriteLine("Payment processing failed - will retry");
                order.ProcessingNotes.Add("Payment processing failed - network timeout");
                return InternalProcessState.Retry;
            }

            Console.WriteLine("Payment processed successfully");
            order.ProcessingNotes.Add("Payment processed successfully");
            return InternalProcessState.Succeeded;
        }

        /// <summary>
        /// Task for checking inventory availability.
        /// </summary>
        private static async Task<InternalProcessState> CheckInventoryTask(Order order, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Checking inventory for order {order.OrderId}...");
            order.ProcessingNotes.Add($"Inventory check started at {DateTime.UtcNow}");

            // Simulate inventory check
            await Task.Delay(150);

            // Check each item in the order
            foreach (var item in order.Items)
            {
                Console.WriteLine($"Checking availability for {item}");

                // Simulate occasional inventory issues
                var random = new Random();
                if (random.NextDouble() < 0.1) // 10% chance of inventory issue
                {
                    Console.WriteLine($"Inventory check failed - {item} out of stock");
                    order.ProcessingNotes.Add($"Inventory check failed - {item} out of stock");
                    return InternalProcessState.Failed;
                }
            }

            Console.WriteLine("All items available in inventory");
            order.ProcessingNotes.Add("All items available in inventory");
            return InternalProcessState.Succeeded;
        }

        /// <summary>
        /// Task for initiating shipping process.
        /// </summary>
        private static async Task<InternalProcessState> InitiateShippingTask(Order order, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Initiating shipping for order {order.OrderId}...");
            order.ProcessingNotes.Add($"Shipping initiation started at {DateTime.UtcNow}");

            // Simulate shipping setup
            await Task.Delay(100);

            Console.WriteLine("Shipping initiated successfully");
            order.ProcessingNotes.Add("Shipping initiated successfully");
            return InternalProcessState.Succeeded;
        }

        /// <summary>
        /// Task for completing the order.
        /// </summary>
        private static async Task<InternalProcessState> CompleteOrderTask(Order order, InternalProcessState currentInternalState)
        {
            Console.WriteLine($"Completing order {order.OrderId}...");
            order.ProcessingNotes.Add($"Order completion started at {DateTime.UtcNow}");

            // Simulate final processing
            await Task.Delay(50);

            order.CompletedAt = DateTime.UtcNow;
            Console.WriteLine("Order completed successfully");
            order.ProcessingNotes.Add("Order completed successfully");

            return InternalProcessState.Succeeded;
        }
    }
}
