using Microsoft.AspNetCore.Mvc;
using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Extensions;
using shared.Components.InternalProcess.Interfaces;
using shared.Components.InternalProcess.Models;
using shared.Components.MessageBus.Models;
using shared.Controllers;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Examples
{
    /// <summary>
    /// Example demonstrating how to integrate the InternalProcess state machine with controllers.
    /// This shows how to replace the existing StateMachine usage in SuperController.
    /// </summary>
    public class ControllerIntegrationExample
    {
        // Example enum for user registration states
        public enum UserRegistrationState
        {
            Created,
            EmailValidation,
            ProfileSetup,
            AccountActivation,
            Completed,
            Failed
        }

        // Example user registration class that implements IStateful
        public class UserRegistration : IStateful<UserRegistrationState>
        {
            public string UserId { get; set; } = Guid.NewGuid().ToString();
            public string Email { get; set; } = string.Empty;
            public string FirstName { get; set; } = string.Empty;
            public string LastName { get; set; } = string.Empty;
            public UserRegistrationState Status { get; set; } = UserRegistrationState.Created;
            public string ActivationToken { get; set; } = string.Empty;
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public List<string> ValidationErrors { get; set; } = new List<string>();
        }

        /// <summary>
        /// Example controller showing how to use the InternalProcess state machine.
        /// This replaces the pattern shown in SuperController.InternalProcess method.
        /// </summary>
        public class UserRegistrationController : ControllerBase
        {
            private readonly IInternalProcessStateMachineFactory _stateMachineFactory;

            public UserRegistrationController(IInternalProcessStateMachineFactory stateMachineFactory)
            {
                _stateMachineFactory = stateMachineFactory;
            }

            /// <summary>
            /// Processes a user registration using the InternalProcess state machine.
            /// This method demonstrates the new pattern for internal process handling.
            /// </summary>
            [HttpPost("process-registration")]
            public async Task<IActionResult> ProcessRegistration(MessageBusMessage message)
            {
                try
                {
                    // Extract the payload from the message
                    var userRegistration = message.GetPayload<UserRegistration>();
                    if (userRegistration == null)
                    {
                        return BadRequest("Invalid user registration data");
                    }

                    // Create and configure the state machine
                    var stateMachine = _stateMachineFactory.Create(userRegistration, $"USER_REG_{userRegistration.UserId}");
                    ConfigureUserRegistrationStateMachine(stateMachine);

                    // Validate the state machine configuration
                    var validationErrors = stateMachine.ValidateConfiguration();
                    if (validationErrors.Any())
                    {
                        return BadRequest($"State machine configuration error: {string.Join("; ", validationErrors)}");
                    }

                    // Execute one iteration of the state machine
                    var success = await stateMachine.ExecuteIteration();
                    
                    if (!success)
                    {
                        return StatusCode(500, "Internal process execution failed");
                    }

                    // Handle the result based on the current state
                    return await HandleRegistrationResult(stateMachine, message);
                }
                catch (InternalProcessException ex)
                {
                    return StatusCode(500, $"Internal process error: {ex.Message}");
                }
                catch (Exception ex)
                {
                    return StatusCode(500, $"Unexpected error: {ex.Message}");
                }
            }

            /// <summary>
            /// Configures the user registration state machine with states and transitions.
            /// </summary>
            private void ConfigureUserRegistrationStateMachine(IInternalProcessStateMachine<UserRegistrationState, UserRegistration> stateMachine)
            {
                // Configure states
                stateMachine
                    .ConfigureState(UserRegistrationState.Created, ValidateEmailTask, "Validate email address")
                    .ConfigureState(UserRegistrationState.EmailValidation, SetupProfileTask, "Setup user profile")
                    .ConfigureState(UserRegistrationState.ProfileSetup, ActivateAccountTask, "Activate user account")
                    .ConfigureState(UserRegistrationState.AccountActivation, CompleteRegistrationTask, "Complete registration");

                // Configure transitions
                stateMachine
                    .AddTransition(UserRegistrationState.Created, InternalProcessState.Succeeded, UserRegistrationState.EmailValidation)
                    .AddTransition(UserRegistrationState.Created, InternalProcessState.Failed, UserRegistrationState.Failed)
                    .AddTransition(UserRegistrationState.EmailValidation, InternalProcessState.Succeeded, UserRegistrationState.ProfileSetup)
                    .AddTransition(UserRegistrationState.EmailValidation, InternalProcessState.Failed, UserRegistrationState.Failed)
                    .AddTransition(UserRegistrationState.ProfileSetup, InternalProcessState.Succeeded, UserRegistrationState.AccountActivation)
                    .AddTransition(UserRegistrationState.ProfileSetup, InternalProcessState.Failed, UserRegistrationState.Failed)
                    .AddTransition(UserRegistrationState.AccountActivation, InternalProcessState.Succeeded, UserRegistrationState.Completed)
                    .AddTransition(UserRegistrationState.AccountActivation, InternalProcessState.Failed, UserRegistrationState.Failed);

                // Add final transitions
                stateMachine
                    .AddFinalTransition(UserRegistrationState.Completed, InternalProcessState.Succeeded, "Registration completed")
                    .AddFinalTransition(UserRegistrationState.Failed, InternalProcessState.Failed, "Registration failed");
            }

            /// <summary>
            /// Handles the result of the state machine execution and determines the appropriate response.
            /// </summary>
            private async Task<IActionResult> HandleRegistrationResult(
                IInternalProcessStateMachine<UserRegistrationState, UserRegistration> stateMachine, 
                MessageBusMessage originalMessage)
            {
                var userRegistration = stateMachine.StateData.StatefulObject;
                var currentState = stateMachine.CurrentState;
                var internalState = stateMachine.InternalState;

                // If the process is finished, return the final result
                if (stateMachine.IsFinished)
                {
                    if (currentState == UserRegistrationState.Completed)
                    {
                        return Ok(new { 
                            Message = "User registration completed successfully", 
                            UserId = userRegistration.UserId,
                            Status = currentState.ToString()
                        });
                    }
                    else
                    {
                        return BadRequest(new { 
                            Message = "User registration failed", 
                            Errors = userRegistration.ValidationErrors,
                            Status = currentState.ToString()
                        });
                    }
                }

                // If we need to continue processing, we could:
                // 1. Queue the next step for async processing
                // 2. Return a "processing" status to the client
                // 3. Continue with synchronous processing

                // For this example, we'll return a processing status
                return Accepted(new { 
                    Message = "Registration is being processed", 
                    UserId = userRegistration.UserId,
                    CurrentState = currentState.ToString(),
                    InternalState = internalState.ToString()
                });
            }

            /// <summary>
            /// Task for validating email address.
            /// </summary>
            private static async Task<InternalProcessState> ValidateEmailTask(UserRegistration registration, InternalProcessState currentInternalState)
            {
                // Simulate email validation
                await Task.Delay(100);

                if (string.IsNullOrWhiteSpace(registration.Email) || !registration.Email.Contains("@"))
                {
                    registration.ValidationErrors.Add("Invalid email address");
                    return InternalProcessState.Failed;
                }

                return InternalProcessState.Succeeded;
            }

            /// <summary>
            /// Task for setting up user profile.
            /// </summary>
            private static async Task<InternalProcessState> SetupProfileTask(UserRegistration registration, InternalProcessState currentInternalState)
            {
                // Simulate profile setup
                await Task.Delay(150);

                if (string.IsNullOrWhiteSpace(registration.FirstName) || string.IsNullOrWhiteSpace(registration.LastName))
                {
                    registration.ValidationErrors.Add("First name and last name are required");
                    return InternalProcessState.Failed;
                }

                return InternalProcessState.Succeeded;
            }

            /// <summary>
            /// Task for activating user account.
            /// </summary>
            private static async Task<InternalProcessState> ActivateAccountTask(UserRegistration registration, InternalProcessState currentInternalState)
            {
                // Simulate account activation
                await Task.Delay(100);

                registration.ActivationToken = Guid.NewGuid().ToString();
                return InternalProcessState.Succeeded;
            }

            /// <summary>
            /// Task for completing registration.
            /// </summary>
            private static async Task<InternalProcessState> CompleteRegistrationTask(UserRegistration registration, InternalProcessState currentInternalState)
            {
                // Simulate final registration steps
                await Task.Delay(50);

                // Mark as completed
                return InternalProcessState.Succeeded;
            }
        }

        /// <summary>
        /// Example of how to register the services in Startup.cs or Program.cs
        /// </summary>
        public static class ServiceRegistrationExample
        {
            public static void ConfigureServices(IServiceCollection services)
            {
                // Register the InternalProcess services
                services.AddInternalProcess();

                // Register other services as needed
                // services.AddScoped<UserRegistrationController>();
            }
        }
    }
}
