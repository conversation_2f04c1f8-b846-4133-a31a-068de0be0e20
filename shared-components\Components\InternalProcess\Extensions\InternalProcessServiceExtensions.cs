using Microsoft.Extensions.DependencyInjection;
using shared.Components.InternalProcess.Interfaces;
using shared.Models.Interfaces;

namespace shared.Components.InternalProcess.Extensions
{
    /// <summary>
    /// Extension methods for registering InternalProcess components with dependency injection.
    /// </summary>
    public static class InternalProcessServiceExtensions
    {
        /// <summary>
        /// Adds InternalProcess state machine services to the service collection.
        /// Registers the state machine as a transient service since each process should have its own instance.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddInternalProcessStateMachine(this IServiceCollection services)
        {
            // Register the state machine as transient since each process needs its own instance
            services.AddTransient(typeof(IInternalProcessStateMachine<,>), typeof(InternalProcessStateMachine<,>));
            
            return services;
        }

        /// <summary>
        /// Adds InternalProcess state machine services with a factory method.
        /// This allows for more complex initialization scenarios.
        /// </summary>
        /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
        /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
        /// <param name="services">The service collection</param>
        /// <param name="factory">Factory method to create the state machine</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddInternalProcessStateMachine<TState, TObject>(
            this IServiceCollection services,
            Func<IServiceProvider, IInternalProcessStateMachine<TState, TObject>> factory)
            where TObject : IStateful<TState>
            where TState : notnull
        {
            services.AddTransient(factory);
            return services;
        }

        /// <summary>
        /// Adds a singleton factory for creating InternalProcess state machines.
        /// This is useful when you need to create multiple state machines with different configurations.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddInternalProcessStateMachineFactory(this IServiceCollection services)
        {
            services.AddSingleton<IInternalProcessStateMachineFactory, InternalProcessStateMachineFactory>();
            return services;
        }

        /// <summary>
        /// Adds all InternalProcess services including the state machine and factory.
        /// This is the recommended way to register all InternalProcess components.
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <returns>The service collection for method chaining</returns>
        public static IServiceCollection AddInternalProcess(this IServiceCollection services)
        {
            return services
                .AddInternalProcessStateMachine()
                .AddInternalProcessStateMachineFactory();
        }
    }

    /// <summary>
    /// Factory interface for creating InternalProcess state machines.
    /// </summary>
    public interface IInternalProcessStateMachineFactory
    {
        /// <summary>
        /// Creates a new state machine with the given payload object.
        /// </summary>
        /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
        /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
        /// <param name="statefulObject">The IStateful object to manage</param>
        /// <param name="correlationId">Optional correlation ID for tracking</param>
        /// <returns>A new state machine instance</returns>
        IInternalProcessStateMachine<TState, TObject> Create<TState, TObject>(
            TObject statefulObject, 
            string? correlationId = null)
            where TObject : IStateful<TState>
            where TState : notnull;

        /// <summary>
        /// Creates a new state machine with existing state data.
        /// </summary>
        /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
        /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
        /// <param name="stateData">Existing state data to load</param>
        /// <returns>A new state machine instance</returns>
        IInternalProcessStateMachine<TState, TObject> Create<TState, TObject>(
            Models.InternalProcessStateData<TState, TObject> stateData)
            where TObject : IStateful<TState>
            where TState : notnull;
    }

    /// <summary>
    /// Default implementation of the InternalProcess state machine factory.
    /// </summary>
    public class InternalProcessStateMachineFactory : IInternalProcessStateMachineFactory
    {
        /// <summary>
        /// Creates a new state machine with the given payload object.
        /// </summary>
        public IInternalProcessStateMachine<TState, TObject> Create<TState, TObject>(
            TObject statefulObject, 
            string? correlationId = null)
            where TObject : IStateful<TState>
            where TState : notnull
        {
            return new InternalProcessStateMachine<TState, TObject>(statefulObject, correlationId);
        }

        /// <summary>
        /// Creates a new state machine with existing state data.
        /// </summary>
        public IInternalProcessStateMachine<TState, TObject> Create<TState, TObject>(
            Models.InternalProcessStateData<TState, TObject> stateData)
            where TObject : IStateful<TState>
            where TState : notnull
        {
            return new InternalProcessStateMachine<TState, TObject>(stateData);
        }
    }
}
