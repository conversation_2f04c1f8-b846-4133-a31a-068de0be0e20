using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Interfaces;
using shared.Components.InternalProcess.Models;
using shared.Models.Interfaces;
using System.Collections.Concurrent;

namespace shared.Components.InternalProcess
{
    /// <summary>
    /// Implementation of a generic internal process state machine.
    /// Manages state transitions, task execution, and retry logic for internal processes.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public class InternalProcessStateMachine<TState, TObject> : IInternalProcessStateMachine<TState, TObject>
        where TObject : IStateful<TState>
        where TState : notnull
    {
        private readonly ConcurrentDictionary<TState, StateConfiguration<TState, TObject>> _stateConfigurations;
        private readonly List<StateTransition<TState>> _transitions;
        private readonly object _lockObject = new object();

        private InternalProcessStateData<TState, TObject>? _stateData;
        private InternalProcessState? _lastInternalState;
        private bool _lastTransitionFailed;
        private bool _lastFailureWasRetryLimitExceeded;
        private Exception? _lastException;

        /// <summary>
        /// Gets the current state data of the state machine.
        /// </summary>
        public InternalProcessStateData<TState, TObject> StateData => _stateData ?? throw new InvalidOperationException("State machine has not been initialized. Call Initialize() or LoadStateData() first.");

        /// <summary>
        /// Gets the current state of the IStateful object.
        /// </summary>
        public TState CurrentState => _stateData?.CurrentState ?? throw new InvalidOperationException("State machine has not been initialized. Call Initialize() or LoadStateData() first.");

        /// <summary>
        /// Gets the current internal process state.
        /// </summary>
        public InternalProcessState InternalState => _stateData?.InternalState ?? throw new InvalidOperationException("State machine has not been initialized. Call Initialize() or LoadStateData() first.");

        /// <summary>
        /// Gets whether the state machine has finished processing.
        /// </summary>
        public bool IsFinished => _stateData?.InternalState == InternalProcessState.Finished;

        /// <summary>
        /// Gets the last internal state before the current one (for error tracking).
        /// </summary>
        public InternalProcessState? LastInternalState => _lastInternalState;

        /// <summary>
        /// Gets whether the last transition failed.
        /// </summary>
        public bool LastTransitionFailed => _lastTransitionFailed;

        /// <summary>
        /// Gets whether the last execution iteration failed due to retry limit being exceeded.
        /// </summary>
        public bool LastFailureWasRetryLimitExceeded => _lastFailureWasRetryLimitExceeded;

        /// <summary>
        /// Gets the last exception that occurred during state machine execution, if any.
        /// </summary>
        public Exception? LastException => _lastException;

        /// <summary>
        /// Initializes a new instance of the InternalProcessStateMachine without an initial object.
        /// Use Initialize() or LoadStateData() to set up the state machine before use.
        /// </summary>
        public InternalProcessStateMachine()
        {
            _stateConfigurations = new ConcurrentDictionary<TState, StateConfiguration<TState, TObject>>();
            _transitions = new List<StateTransition<TState>>();
            _lastTransitionFailed = false;
            _lastFailureWasRetryLimitExceeded = false;
            _lastException = null;
        }

        /// <summary>
        /// Creates a new InternalProcessStateMachine with the given payload object.
        /// </summary>
        /// <param name="statefulObject">The IStateful object to manage</param>
        /// <param name="correlationId">Optional correlation ID for tracking</param>
        public InternalProcessStateMachine(TObject statefulObject, string? correlationId = null)
        {
            _stateConfigurations = new ConcurrentDictionary<TState, StateConfiguration<TState, TObject>>();
            _transitions = new List<StateTransition<TState>>();
            _stateData = new InternalProcessStateData<TState, TObject>(statefulObject, correlationId);
            _lastTransitionFailed = false;
            _lastFailureWasRetryLimitExceeded = false;
            _lastException = null;
        }

        /// <summary>
        /// Creates a new InternalProcessStateMachine with existing state data.
        /// </summary>
        /// <param name="stateData">Existing state data to load</param>
        public InternalProcessStateMachine(InternalProcessStateData<TState, TObject> stateData)
        {
            _stateConfigurations = new ConcurrentDictionary<TState, StateConfiguration<TState, TObject>>();
            _transitions = new List<StateTransition<TState>>();
            _stateData = stateData ?? throw new ArgumentNullException(nameof(stateData));
            _lastTransitionFailed = false;
            _lastFailureWasRetryLimitExceeded = false;
            _lastException = null;
        }

        /// <summary>
        /// Configures a state with its associated task.
        /// </summary>
        public IInternalProcessStateMachine<TState, TObject> ConfigureState(
            TState state,
            Func<TObject, InternalProcessState, Task<(InternalProcessState state, object? result)>> stateTask,
            string? description = null,
            TimeSpan? taskTimeout = null)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (stateTask == null) throw new ArgumentNullException(nameof(stateTask));

            var configuration = new StateConfiguration<TState, TObject>(state, stateTask, description, taskTimeout);
            _stateConfigurations.AddOrUpdate(state, configuration, (key, existing) => configuration);

            return this;
        }

        /// <summary>
        /// Adds a transition between states.
        /// </summary>
        public IInternalProcessStateMachine<TState, TObject> AddTransition(
            TState fromState,
            InternalProcessState trigger,
            TState toState,
            string? description = null)
        {
            if (fromState == null) throw new ArgumentNullException(nameof(fromState));
            if (toState == null) throw new ArgumentNullException(nameof(toState));

            lock (_lockObject)
            {
                var transition = new StateTransition<TState>(fromState, trigger, toState, false, description);
                _transitions.Add(transition);
            }

            return this;
        }

        /// <summary>
        /// Adds a final transition that marks the process as finished.
        /// </summary>
        public IInternalProcessStateMachine<TState, TObject> AddFinalTransition(
            TState fromState,
            InternalProcessState trigger,
            string? description = null)
        {
            if (fromState == null) throw new ArgumentNullException(nameof(fromState));

            lock (_lockObject)
            {
                var transition = new StateTransition<TState>(fromState, trigger, fromState, true, description);
                _transitions.Add(transition);
            }

            return this;
        }

        /// <summary>
        /// Sets the current state of the state machine and the IStateful object.
        /// </summary>
        public void SetCurrentState(TState state, InternalProcessState internalState = InternalProcessState.Succeeded)
        {
            if (state == null) throw new ArgumentNullException(nameof(state));
            if (_stateData == null) throw new InvalidOperationException("State machine has not been initialized. Call Initialize() or LoadStateData() first.");

            lock (_lockObject)
            {
                _stateData.StatefulObject.Status = state;
                _stateData.UpdateInternalState(internalState);
                _lastTransitionFailed = false;
                _lastFailureWasRetryLimitExceeded = false;
                _lastException = null;
            }
        }

        /// <summary>
        /// Loads the complete state data into the state machine.
        /// </summary>
        public void LoadStateData(InternalProcessStateData<TState, TObject> stateData)
        {
            _stateData = stateData ?? throw new ArgumentNullException(nameof(stateData));
            _lastTransitionFailed = false;
            _lastFailureWasRetryLimitExceeded = false;
            _lastException = null;
        }

        /// <summary>
        /// Initializes the state machine with a TObject instance.
        /// This is useful when the state machine was created without an initial object.
        /// </summary>
        public void Initialize(TObject statefulObject, string? correlationId = null)
        {
            if (statefulObject == null) throw new ArgumentNullException(nameof(statefulObject));
            if (_stateData != null) throw new InvalidOperationException("State machine has already been initialized. Use LoadStateData() to replace existing state.");

            _stateData = new InternalProcessStateData<TState, TObject>(statefulObject, correlationId);
            _lastTransitionFailed = false;
            _lastFailureWasRetryLimitExceeded = false;
            _lastException = null;
        }

        /// <summary>
        /// Executes the task for the current state and returns the resulting internal state and result object.
        /// </summary>
        private async Task<(InternalProcessState state, object? result)> RunStateTask()
        {
            if (_stateData == null) throw new InvalidOperationException("State machine has not been initialized. Call Initialize() or LoadStateData() first.");

            var currentState = _stateData.CurrentState;

            if (!_stateConfigurations.TryGetValue(currentState, out var stateConfig))
            {
                throw new StateConfigurationException(currentState);
            }

            try
            {
                // Execute the state task with or without timeout based on configuration
                (InternalProcessState state, object? result) taskResult;

                if (HasNoTimeout(stateConfig.TaskTimeout))
                {
                    // No timeout specified, execute directly
                    taskResult = await stateConfig.StateTask(_stateData.Payload, _stateData.InternalState);
                }
                else
                {
                    // Execute with timeout
                    taskResult = await ExecuteWithTimeout(
                        stateConfig.StateTask(_stateData.Payload, _stateData.InternalState),
                        stateConfig.TaskTimeout);
                }

                return taskResult;
            }
            catch (TimeoutException)
            {
                throw new StateTaskExecutionException(
                    currentState,
                    _stateData.InternalState,
                    $"State task for '{currentState}' timed out after {stateConfig.TaskTimeout}");
            }
            catch (Exception ex) when (!(ex is InternalProcessException))
            {
                throw new StateTaskExecutionException(
                    currentState,
                    _stateData.InternalState,
                    $"State task for '{currentState}' failed with error: {ex.Message}",
                    ex);
            }
        }

        /// <summary>
        /// Executes one complete iteration of the state machine.
        /// </summary>
        public async Task<(bool success, object? result)> ExecuteIteration()
        {
            if (_stateData == null) throw new InvalidOperationException("State machine has not been initialized. Call Initialize() or LoadStateData() first.");

            try
            {
                _lastInternalState = _stateData.InternalState;
                _lastTransitionFailed = false;
                _lastFailureWasRetryLimitExceeded = false;
                _lastException = null;

                // Execute the current state task
                var (resultInternalState, taskResult) = await RunStateTask();

                // Handle retry logic
                if (resultInternalState == InternalProcessState.Retry)
                {
                    _stateData.UpdateInternalState(InternalProcessState.Retry);

                    // Check if this retry would exceed the limit
                    if (_stateData.IsRetryLimitExceeded())
                    {
                        _lastFailureWasRetryLimitExceeded = true;
                        _lastException = new MaxRetryExceededException(_stateData.CurrentState, _stateData.RetryCount, _stateData.MaxRetries);
                        return (false, taskResult);
                    }

                    return (true, taskResult); // Retry is considered successful iteration
                }

                // Attempt to transition based on the result
                var transitionSuccessful = TryTransition(resultInternalState);

                if (!transitionSuccessful)
                {
                    _lastTransitionFailed = true;
                    _lastException = new StateTransitionException(_stateData.CurrentState, resultInternalState);
                    return (false, taskResult);
                }

                return (true, taskResult);
            }
            catch (Exception ex)
            {
                _lastException = ex;

                if (ex is MaxRetryExceededException)
                {
                    _lastFailureWasRetryLimitExceeded = true;
                }
                else if (ex is StateTransitionException)
                {
                    _lastTransitionFailed = true;
                }

                return (false, null);
            }
        }

        /// <summary>
        /// Executes the state machine until it reaches a finished state or fails.
        /// </summary>
        public async Task<(bool success, object? lastResult)> ExecuteToCompletion(int maxIterations = 100)
        {
            int iterations = 0;
            object? lastResult = null;

            while (!IsFinished && iterations < maxIterations)
            {
                var (success, result) = await ExecuteIteration();
                lastResult = result; // Keep track of the last result

                if (!success)
                {
                    return (false, lastResult);
                }

                iterations++;
            }

            return (IsFinished, lastResult);
        }

        /// <summary>
        /// Attempts to transition to a new state based on the given internal process state.
        /// </summary>
        public bool TryTransition(InternalProcessState internalState)
        {
            lock (_lockObject)
            {
                var currentState = _stateData.CurrentState;
                var transition = _transitions.FirstOrDefault(t => t.Matches(currentState, internalState));

                if (transition == null)
                {
                    return false;
                }

                // Handle final transitions
                if (transition.IsFinalTransition)
                {
                    _stateData.UpdateInternalState(InternalProcessState.Finished);
                }
                else
                {
                    // Update the IStateful object's state
                    _stateData.StatefulObject.Status = transition.ToState;
                    _stateData.UpdateInternalState(internalState);
                }

                return true;
            }
        }

        /// <summary>
        /// Gets all configured transitions from the current state.
        /// </summary>
        public IEnumerable<StateTransition<TState>> GetAvailableTransitions()
        {
            var currentState = _stateData.CurrentState;
            return _transitions.Where(t => EqualityComparer<TState>.Default.Equals(t.FromState, currentState));
        }

        /// <summary>
        /// Gets all configured transitions in the state machine.
        /// </summary>
        public IEnumerable<StateTransition<TState>> GetAllTransitions()
        {
            return _transitions.ToList();
        }

        /// <summary>
        /// Gets all configured states in the state machine.
        /// </summary>
        public IEnumerable<TState> GetConfiguredStates()
        {
            return _stateConfigurations.Keys.ToList();
        }

        /// <summary>
        /// Validates the state machine configuration.
        /// </summary>
        public IEnumerable<string> ValidateConfiguration()
        {
            var errors = new List<string>();

            // Check that all states referenced in transitions have configurations
            var configuredStates = new HashSet<TState>(_stateConfigurations.Keys);
            var referencedStates = new HashSet<TState>();

            foreach (var transition in _transitions)
            {
                referencedStates.Add(transition.FromState);
                if (!transition.IsFinalTransition)
                {
                    referencedStates.Add(transition.ToState);
                }
            }

            foreach (var state in referencedStates)
            {
                if (!configuredStates.Contains(state))
                {
                    errors.Add($"State '{state}' is referenced in transitions but has no configuration");
                }
            }

            // Check for unreachable states (states with no incoming transitions)
            var statesWithIncomingTransitions = new HashSet<TState>();
            foreach (var transition in _transitions.Where(t => !t.IsFinalTransition))
            {
                statesWithIncomingTransitions.Add(transition.ToState);
            }

            foreach (var state in configuredStates)
            {
                if (!statesWithIncomingTransitions.Contains(state) &&
                    !EqualityComparer<TState>.Default.Equals(state, _stateData.CurrentState))
                {
                    errors.Add($"State '{state}' is configured but has no incoming transitions and is not the current state");
                }
            }

            // Check for states with no outgoing transitions (except final states)
            var statesWithOutgoingTransitions = new HashSet<TState>();
            foreach (var transition in _transitions)
            {
                statesWithOutgoingTransitions.Add(transition.FromState);
            }

            foreach (var state in configuredStates)
            {
                if (!statesWithOutgoingTransitions.Contains(state))
                {
                    errors.Add($"State '{state}' has no outgoing transitions - it may be a dead end");
                }
            }

            return errors;
        }

        /// <summary>
        /// Checks if the current state has exceeded its retry limit.
        /// </summary>
        public bool IsRetryLimitExceeded()
        {
            if (_stateData == null) throw new InvalidOperationException("State machine has not been initialized. Call Initialize() or LoadStateData() first.");
            return _stateData.IsRetryLimitExceeded();
        }

        /// <summary>
        /// Gets a detailed description of why the last ExecuteIteration call failed.
        /// </summary>
        public string GetLastFailureReason()
        {
            if (_lastException == null)
            {
                return "No failure recorded";
            }

            if (_lastFailureWasRetryLimitExceeded)
            {
                if (_stateData == null) return "Retry limit exceeded (state data not available)";
                return $"Retry limit exceeded: {_stateData.RetryCount}/{_stateData.MaxRetries} retries attempted for state '{_stateData.CurrentState}'";
            }

            if (_lastTransitionFailed)
            {
                if (_stateData == null) return "Transition failed (state data not available)";
                return $"Transition failed: No valid transition found from state '{_stateData.CurrentState}' with trigger '{_lastInternalState}'";
            }

            if (_lastException is StateTaskExecutionException taskEx)
            {
                return $"State task execution failed for state '{taskEx.State}': {taskEx.Message}";
            }

            if (_lastException is StateConfigurationException configEx)
            {
                return $"State configuration error for state '{configEx.State}': {configEx.Message}";
            }

            return $"Execution failed: {_lastException.Message}";
        }

        /// <summary>
        /// Checks if the timeout value indicates no timeout should be applied.
        /// </summary>
        private static bool HasNoTimeout(TimeSpan timeout)
        {
            return timeout == TimeSpan.Zero ||
                   timeout == Timeout.InfiniteTimeSpan ||
                   timeout == TimeSpan.MaxValue ||
                   timeout.TotalMilliseconds <= 0;
        }

        /// <summary>
        /// Helper method to execute a task with timeout.
        /// </summary>
        private async Task<T> ExecuteWithTimeout<T>(Task<T> task, TimeSpan timeout)
        {
            if (HasNoTimeout(timeout))
            {
                return await task;
            }

            using var cancellationTokenSource = new CancellationTokenSource(timeout);
            var completedTask = await Task.WhenAny(task, Task.Delay(timeout, cancellationTokenSource.Token));

            if (completedTask == task)
            {
                cancellationTokenSource.Cancel(); // Cancel the delay task
                return await task; // Return the result or propagate exception
            }
            else
            {
                throw new TimeoutException($"Task execution timed out after {timeout}");
            }
        }
    }
}
