using shared.Components.InternalProcess.Enums;

namespace shared.Components.InternalProcess.Models
{
    /// <summary>
    /// Base exception for all internal process state machine related errors.
    /// </summary>
    public abstract class InternalProcessException : Exception
    {
        /// <summary>
        /// Creates a new InternalProcessException.
        /// </summary>
        /// <param name="message">Error message</param>
        protected InternalProcessException(string message) : base(message) { }

        /// <summary>
        /// Creates a new InternalProcessException with an inner exception.
        /// </summary>
        /// <param name="message">Error message</param>
        /// <param name="innerException">Inner exception</param>
        protected InternalProcessException(string message, Exception innerException) : base(message, innerException) { }
    }

    /// <summary>
    /// Exception thrown when a state transition cannot be found or is invalid.
    /// </summary>
    public class StateTransitionException : InternalProcessException
    {
        /// <summary>
        /// The state from which the transition was attempted.
        /// </summary>
        public object? FromState { get; }

        /// <summary>
        /// The internal process state that triggered the transition attempt.
        /// </summary>
        public InternalProcessState Trigger { get; }

        /// <summary>
        /// Creates a new StateTransitionException.
        /// </summary>
        /// <param name="fromState">The state from which transition was attempted</param>
        /// <param name="trigger">The trigger that caused the transition attempt</param>
        /// <param name="message">Error message</param>
        public StateTransitionException(object? fromState, InternalProcessState trigger, string message)
            : base(message)
        {
            FromState = fromState;
            Trigger = trigger;
        }

        /// <summary>
        /// Creates a new StateTransitionException with a default message.
        /// </summary>
        /// <param name="fromState">The state from which transition was attempted</param>
        /// <param name="trigger">The trigger that caused the transition attempt</param>
        public StateTransitionException(object? fromState, InternalProcessState trigger)
            : this(fromState, trigger, $"No valid transition found from state '{fromState}' with trigger '{trigger}'")
        {
        }
    }

    /// <summary>
    /// Exception thrown when a state configuration is invalid or missing.
    /// </summary>
    public class StateConfigurationException : InternalProcessException
    {
        /// <summary>
        /// The state that has the configuration issue.
        /// </summary>
        public object? State { get; }

        /// <summary>
        /// Creates a new StateConfigurationException.
        /// </summary>
        /// <param name="state">The state with the configuration issue</param>
        /// <param name="message">Error message</param>
        public StateConfigurationException(object? state, string message)
            : base(message)
        {
            State = state;
        }

        /// <summary>
        /// Creates a new StateConfigurationException for a missing state task.
        /// </summary>
        /// <param name="state">The state missing a task configuration</param>
        public StateConfigurationException(object? state)
            : this(state, $"No task configuration found for state '{state}'")
        {
        }
    }

    /// <summary>
    /// Exception thrown when a state task execution fails or times out.
    /// </summary>
    public class StateTaskExecutionException : InternalProcessException
    {
        /// <summary>
        /// The state whose task failed to execute.
        /// </summary>
        public object? State { get; }

        /// <summary>
        /// The internal process state when the task was executed.
        /// </summary>
        public InternalProcessState InternalState { get; }

        /// <summary>
        /// Creates a new StateTaskExecutionException.
        /// </summary>
        /// <param name="state">The state whose task failed</param>
        /// <param name="internalState">The internal state when execution failed</param>
        /// <param name="message">Error message</param>
        /// <param name="innerException">The original exception that caused the failure</param>
        public StateTaskExecutionException(object? state, InternalProcessState internalState, string message, Exception? innerException = null)
            : base(message, innerException ?? new Exception("State task execution failed"))
        {
            State = state;
            InternalState = internalState;
        }
    }

    /// <summary>
    /// Exception thrown when the state machine configuration is invalid.
    /// </summary>
    public class StateMachineConfigurationException : InternalProcessException
    {
        /// <summary>
        /// List of validation errors found in the configuration.
        /// </summary>
        public IReadOnlyList<string> ValidationErrors { get; }

        /// <summary>
        /// Creates a new StateMachineConfigurationException.
        /// </summary>
        /// <param name="validationErrors">List of validation errors</param>
        public StateMachineConfigurationException(IEnumerable<string> validationErrors)
            : base($"State machine configuration is invalid: {string.Join("; ", validationErrors)}")
        {
            ValidationErrors = validationErrors.ToList().AsReadOnly();
        }

        /// <summary>
        /// Creates a new StateMachineConfigurationException with a single error.
        /// </summary>
        /// <param name="error">The validation error</param>
        public StateMachineConfigurationException(string error)
            : this(new[] { error })
        {
        }
    }

    /// <summary>
    /// Exception thrown when the maximum retry limit is exceeded.
    /// </summary>
    public class MaxRetryExceededException : InternalProcessException
    {
        /// <summary>
        /// The state where the retry limit was exceeded.
        /// </summary>
        public object? State { get; }

        /// <summary>
        /// The number of retries that were attempted.
        /// </summary>
        public int RetryCount { get; }

        /// <summary>
        /// The maximum number of retries allowed.
        /// </summary>
        public int MaxRetries { get; }

        /// <summary>
        /// Creates a new MaxRetryExceededException.
        /// </summary>
        /// <param name="state">The state where retry limit was exceeded</param>
        /// <param name="retryCount">Number of retries attempted</param>
        /// <param name="maxRetries">Maximum retries allowed</param>
        public MaxRetryExceededException(object? state, int retryCount, int maxRetries)
            : base($"Maximum retry limit ({maxRetries}) exceeded for state '{state}'. Attempted {retryCount} retries.")
        {
            State = state;
            RetryCount = retryCount;
            MaxRetries = maxRetries;
        }
    }
}
