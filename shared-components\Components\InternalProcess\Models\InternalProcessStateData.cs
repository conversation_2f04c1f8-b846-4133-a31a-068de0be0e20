using Amazon.DynamoDBv2.DataModel;
using shared.Components.InternalProcess.Enums;
using shared.Converters;
using shared.Models.Interfaces;
using System.Text.Json.Serialization;

namespace shared.Components.InternalProcess.Models
{
    /// <summary>
    /// Represents the complete state data for an internal process state machine.
    /// This class is serializable and can be easily saved/loaded for persistence.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public class InternalProcessStateData<TState, TObject> 
        where TObject : IStateful<TState>
    {
        /// <summary>
        /// The current internal process state that controls state machine flow.
        /// </summary>
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<InternalProcessState>))]
        [JsonConverter(typeof(JsonEnumStringConverter<InternalProcessState>))]
        public InternalProcessState InternalState { get; set; } = InternalProcessState.Succeeded;

        /// <summary>
        /// The IStateful object that tracks the actual business state.
        /// This object's Status property will be updated during state transitions.
        /// </summary>
        [JsonConverter(typeof(JsonTypedConverter<object>))]
        public TObject StatefulObject { get; set; }

        /// <summary>
        /// The payload data for the state machine.
        /// This is the same object as StatefulObject but provides a generic interface.
        /// </summary>
        [JsonIgnore]
        public TObject Payload => StatefulObject;

        /// <summary>
        /// The current state of the state machine (from the IStateful object).
        /// </summary>
        [JsonIgnore]
        public TState CurrentState => StatefulObject.Status;

        /// <summary>
        /// Number of times the current state has been retried.
        /// This is incremented when InternalState is set to Retry.
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// Maximum number of retries allowed for any state.
        /// Default is 3 retries.
        /// </summary>
        public int MaxRetries { get; set; } = 3;

        /// <summary>
        /// Timestamp when the state data was last updated.
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Optional correlation ID for tracking the state machine execution.
        /// </summary>
        public string? CorrelationId { get; set; }

        /// <summary>
        /// Creates a new instance of InternalProcessStateData.
        /// </summary>
        /// <param name="statefulObject">The IStateful object to track</param>
        /// <param name="correlationId">Optional correlation ID</param>
        public InternalProcessStateData(TObject statefulObject, string? correlationId = null)
        {
            StatefulObject = statefulObject ?? throw new ArgumentNullException(nameof(statefulObject));
            CorrelationId = correlationId;
        }

        /// <summary>
        /// Parameterless constructor for serialization.
        /// </summary>
        public InternalProcessStateData()
        {
            StatefulObject = default(TObject)!;
        }

        /// <summary>
        /// Updates the internal state and manages retry counting.
        /// </summary>
        /// <param name="newInternalState">The new internal state</param>
        public void UpdateInternalState(InternalProcessState newInternalState)
        {
            if (newInternalState == InternalProcessState.Retry)
            {
                RetryCount++;
            }
            else if (InternalState == InternalProcessState.Retry && newInternalState != InternalProcessState.Retry)
            {
                // Reset retry count when moving away from retry state
                RetryCount = 0;
            }

            InternalState = newInternalState;
            LastUpdated = DateTime.UtcNow;
        }

        /// <summary>
        /// Checks if the maximum retry limit has been reached.
        /// </summary>
        /// <returns>True if retry limit exceeded</returns>
        public bool IsRetryLimitExceeded()
        {
            return RetryCount >= MaxRetries;
        }

        /// <summary>
        /// Resets the retry count to zero.
        /// </summary>
        public void ResetRetryCount()
        {
            RetryCount = 0;
            LastUpdated = DateTime.UtcNow;
        }
    }
}
