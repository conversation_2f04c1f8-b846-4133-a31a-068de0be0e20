using shared.Components.InternalProcess.Enums;
using System.Text.Json.Serialization;

namespace shared.Components.InternalProcess.Models
{
    /// <summary>
    /// Represents a state transition definition in the state machine.
    /// Defines how the state machine moves from one state to another based on internal process triggers.
    /// </summary>
    /// <typeparam name="TState">The type of state used in the state machine</typeparam>
    public class StateTransition<TState> where TState : notnull
    {
        /// <summary>
        /// The source state from which this transition can occur.
        /// </summary>
        public TState FromState { get; set; }

        /// <summary>
        /// The internal process state that triggers this transition.
        /// </summary>
        public InternalProcessState Trigger { get; set; }

        /// <summary>
        /// The destination state to transition to when the trigger occurs.
        /// </summary>
        public TState ToState { get; set; }

        /// <summary>
        /// Indicates whether this is a final transition that sets the internal state to Finished.
        /// Final transitions don't change the IStateful object's state but mark the process as complete.
        /// </summary>
        public bool IsFinalTransition { get; set; } = false;

        /// <summary>
        /// Optional description of what this transition represents.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Creates a new state transition.
        /// </summary>
        /// <param name="fromState">Source state</param>
        /// <param name="trigger">Trigger that causes the transition</param>
        /// <param name="toState">Destination state</param>
        /// <param name="isFinalTransition">Whether this is a final transition</param>
        /// <param name="description">Optional description</param>
        public StateTransition(TState fromState, InternalProcessState trigger, TState toState, 
            bool isFinalTransition = false, string? description = null)
        {
            FromState = fromState;
            Trigger = trigger;
            ToState = toState;
            IsFinalTransition = isFinalTransition;
            Description = description;
        }

        /// <summary>
        /// Parameterless constructor for serialization.
        /// </summary>
        public StateTransition()
        {
            FromState = default(TState)!;
            ToState = default(TState)!;
        }

        /// <summary>
        /// Checks if this transition matches the given state and trigger.
        /// </summary>
        /// <param name="currentState">Current state to check</param>
        /// <param name="trigger">Trigger to check</param>
        /// <returns>True if this transition applies</returns>
        public bool Matches(TState currentState, InternalProcessState trigger)
        {
            return EqualityComparer<TState>.Default.Equals(FromState, currentState) && Trigger == trigger;
        }

        /// <summary>
        /// Returns a string representation of this transition.
        /// </summary>
        public override string ToString()
        {
            var finalIndicator = IsFinalTransition ? " (Final)" : "";
            return $"{FromState} --[{Trigger}]--> {ToState}{finalIndicator}";
        }
    }

    /// <summary>
    /// Represents the configuration for a state in the state machine.
    /// Each state has an associated task that must be executed before transitions can occur.
    /// </summary>
    /// <typeparam name="TState">The type of state used in the state machine</typeparam>
    /// <typeparam name="TObject">The type of the payload object</typeparam>
    public class StateConfiguration<TState, TObject> where TState : notnull
    {
        /// <summary>
        /// The state this configuration applies to.
        /// </summary>
        public TState State { get; set; }

        /// <summary>
        /// The task function to execute when entering this state.
        /// Takes the payload object and current internal state, returns a tuple with the new internal state and an optional result object.
        /// </summary>
        public Func<TObject, InternalProcessState, Task<(InternalProcessState state, object? result)>> StateTask { get; set; }

        /// <summary>
        /// Optional description of what this state does.
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Maximum execution time allowed for the state task.
        /// Default is TimeSpan.Zero (no timeout).
        /// </summary>
        public TimeSpan TaskTimeout { get; set; } = TimeSpan.Zero;

        /// <summary>
        /// Creates a new state configuration.
        /// </summary>
        /// <param name="state">The state this configuration applies to</param>
        /// <param name="stateTask">The task to execute for this state</param>
        /// <param name="description">Optional description</param>
        /// <param name="taskTimeout">Maximum execution time for the task</param>
        public StateConfiguration(TState state,
            Func<TObject, InternalProcessState, Task<(InternalProcessState state, object? result)>> stateTask,
            string? description = null,
            TimeSpan? taskTimeout = null)
        {
            State = state;
            StateTask = stateTask ?? throw new ArgumentNullException(nameof(stateTask));
            Description = description;
            TaskTimeout = taskTimeout ?? TimeSpan.Zero; // Default to no timeout
        }

        /// <summary>
        /// Parameterless constructor for serialization.
        /// </summary>
        public StateConfiguration()
        {
            State = default(TState)!;
            StateTask = null!;
        }
    }
}
