using Microsoft.VisualStudio.TestTools.UnitTesting;
using shared.Components.InternalProcess;
using shared.Components.InternalProcess.Enums;
using shared.Components.InternalProcess.Models;
using shared.Interfaces;
using System;
using System.Threading.Tasks;

namespace shared.Components.InternalProcess.Tests
{
    [TestClass]
    public class ParameterlessConstructorTests
    {
        public enum TestState
        {
            Initial,
            Processing,
            Completed
        }

        public class TestObject : IStateful<TestState>
        {
            public TestState Status { get; set; } = TestState.Initial;
        }

        [TestMethod]
        public void ParameterlessConstructor_CreatesUninitializedStateMachine()
        {
            // Arrange & Act
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();

            // Assert
            Assert.ThrowsException<InvalidOperationException>(() => _ = stateMachine.StateData);
            Assert.ThrowsException<InvalidOperationException>(() => _ = stateMachine.CurrentState);
            Assert.ThrowsException<InvalidOperationException>(() => _ = stateMachine.InternalState);
            Assert.IsFalse(stateMachine.IsFinished); // Should not throw, returns false for null state
        }

        [TestMethod]
        public void Initialize_WithValidObject_InitializesStateMachine()
        {
            // Arrange
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();
            var testObject = new TestObject();

            // Act
            stateMachine.Initialize(testObject, "test-correlation-id");

            // Assert
            Assert.IsNotNull(stateMachine.StateData);
            Assert.AreEqual(TestState.Initial, stateMachine.CurrentState);
            Assert.AreEqual(InternalProcessState.Succeeded, stateMachine.InternalState);
            Assert.AreEqual("test-correlation-id", stateMachine.StateData.CorrelationId);
            Assert.AreSame(testObject, stateMachine.StateData.StatefulObject);
        }

        [TestMethod]
        public void Initialize_WhenAlreadyInitialized_ThrowsException()
        {
            // Arrange
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();
            var testObject1 = new TestObject();
            var testObject2 = new TestObject();
            stateMachine.Initialize(testObject1);

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() => stateMachine.Initialize(testObject2));
        }

        [TestMethod]
        public void Initialize_WithNullObject_ThrowsException()
        {
            // Arrange
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();

            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => stateMachine.Initialize(null!));
        }

        [TestMethod]
        public async Task ExecuteIteration_WithoutInitialization_ThrowsException()
        {
            // Arrange
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();

            // Act & Assert
            await Assert.ThrowsExceptionAsync<InvalidOperationException>(
                async () => await stateMachine.ExecuteIteration());
        }

        [TestMethod]
        public void IsRetryLimitExceeded_WithoutInitialization_ThrowsException()
        {
            // Arrange
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() => stateMachine.IsRetryLimitExceeded());
        }

        [TestMethod]
        public void SetCurrentState_WithoutInitialization_ThrowsException()
        {
            // Arrange
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();

            // Act & Assert
            Assert.ThrowsException<InvalidOperationException>(() => 
                stateMachine.SetCurrentState(TestState.Processing));
        }

        [TestMethod]
        public async Task FactoryPattern_WorksCorrectly()
        {
            // Arrange - Simulate factory creating state machine without object
            var stateMachine = new InternalProcessStateMachine<TestState, TestObject>();
            
            // Configure the state machine before initialization
            stateMachine.ConfigureState(TestState.Initial, TestTask);
            stateMachine.AddTransition(TestState.Initial, InternalProcessState.Succeeded, TestState.Processing);

            // Act - Later initialize with actual object (simulating loading from database)
            var testObject = new TestObject();
            stateMachine.Initialize(testObject);

            // Execute iteration
            var (success, result) = await stateMachine.ExecuteIteration();

            // Assert
            Assert.IsTrue(success);
            Assert.AreEqual("TestTask completed", result);
            Assert.AreEqual(TestState.Processing, stateMachine.CurrentState);
        }

        private static async Task<(InternalProcessState, object?)> TestTask(TestObject obj, InternalProcessState currentState)
        {
            await Task.Delay(1);
            return (InternalProcessState.Succeeded, "TestTask completed");
        }
    }
}
