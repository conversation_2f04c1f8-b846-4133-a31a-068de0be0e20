using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Interfaces;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Builders
{
    /// <summary>
    /// Builder class for creating and configuring multi-step processes.
    /// Provides a fluent interface for step configuration and process setup.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public class MultiStepProcessBuilder<TState, TObject>
        where TObject : IStateful<TState>
        where TState : notnull
    {
        private readonly MultiStepProcessConfiguration _configuration;
        private readonly List<StepConfiguration<TState, TObject>> _stepConfigurations;
        private TState[]? _stateSequence;

        /// <summary>
        /// Creates a new MultiStepProcessBuilder with default configuration.
        /// </summary>
        public MultiStepProcessBuilder() : this(new MultiStepProcessConfiguration())
        {
        }

        /// <summary>
        /// Creates a new MultiStepProcessBuilder with specified configuration.
        /// </summary>
        /// <param name="configuration">Process configuration</param>
        public MultiStepProcessBuilder(MultiStepProcessConfiguration configuration)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _stepConfigurations = new List<StepConfiguration<TState, TObject>>();
        }

        /// <summary>
        /// Adds a step to the process.
        /// </summary>
        /// <param name="state">The state this step applies to</param>
        /// <param name="stateTask">Task to execute when moving forward</param>
        /// <param name="rollbackTask">Task to execute when rolling back</param>
        /// <param name="description">Optional description of the step</param>
        /// <returns>This builder instance for method chaining</returns>
        public MultiStepProcessBuilder<TState, TObject> AddStep(
            TState state,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> stateTask,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> rollbackTask,
            string? description = null)
        {
            _stepConfigurations.Add(new StepConfiguration<TState, TObject>
            {
                State = state,
                StateTask = stateTask,
                RollbackTask = rollbackTask,
                Description = description
            });

            return this;
        }

        /// <summary>
        /// Adds a step with timeouts to the process.
        /// </summary>
        /// <param name="state">The state this step applies to</param>
        /// <param name="stateTask">Task to execute when moving forward</param>
        /// <param name="rollbackTask">Task to execute when rolling back</param>
        /// <param name="stateTaskTimeout">Timeout for the StateTask</param>
        /// <param name="rollbackTaskTimeout">Timeout for the RollbackTask</param>
        /// <param name="description">Optional description of the step</param>
        /// <returns>This builder instance for method chaining</returns>
        public MultiStepProcessBuilder<TState, TObject> AddStep(
            TState state,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> stateTask,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> rollbackTask,
            TimeSpan stateTaskTimeout,
            TimeSpan rollbackTaskTimeout,
            string? description = null)
        {
            _stepConfigurations.Add(new StepConfiguration<TState, TObject>
            {
                State = state,
                StateTask = stateTask,
                RollbackTask = rollbackTask,
                StateTaskTimeout = stateTaskTimeout,
                RollbackTaskTimeout = rollbackTaskTimeout,
                Description = description
            });

            return this;
        }

        /// <summary>
        /// Defines the sequence of states for the process.
        /// </summary>
        /// <param name="stateSequence">Array of states in execution order</param>
        /// <returns>This builder instance for method chaining</returns>
        public MultiStepProcessBuilder<TState, TObject> WithSequence(params TState[] stateSequence)
        {
            _stateSequence = stateSequence ?? throw new ArgumentNullException(nameof(stateSequence));
            return this;
        }

        /// <summary>
        /// Sets the maximum number of retries for the process.
        /// </summary>
        /// <param name="maxRetries">Maximum retries per step</param>
        /// <returns>This builder instance for method chaining</returns>
        public MultiStepProcessBuilder<TState, TObject> WithMaxRetries(int maxRetries)
        {
            _configuration.MaxRetries = maxRetries;
            return this;
        }

        /// <summary>
        /// Sets the timeout behavior for the process.
        /// </summary>
        /// <param name="timeoutBehavior">How to handle timeouts</param>
        /// <returns>This builder instance for method chaining</returns>
        public MultiStepProcessBuilder<TState, TObject> WithTimeoutBehavior(MultiStepProcessTaskRunResult timeoutBehavior)
        {
            _configuration.TimeoutBehavior = timeoutBehavior;
            return this;
        }

        /// <summary>
        /// Sets the default task timeout for the process.
        /// </summary>
        /// <param name="defaultTimeout">Default timeout for tasks</param>
        /// <returns>This builder instance for method chaining</returns>
        public MultiStepProcessBuilder<TState, TObject> WithDefaultTimeout(TimeSpan defaultTimeout)
        {
            _configuration.DefaultTaskTimeout = defaultTimeout;
            return this;
        }

        /// <summary>
        /// Builds the configured multi-step process.
        /// </summary>
        /// <returns>A new IMultiStepProcess instance</returns>
        /// <exception cref="InvalidOperationException">Thrown when configuration is invalid</exception>
        public IMultiStepProcess<TState, TObject> Build()
        {
            if (_stepConfigurations.Count == 0)
                throw new InvalidOperationException("No steps have been configured. Use AddStep() to add steps.");

            if (_stateSequence == null || _stateSequence.Length == 0)
                throw new InvalidOperationException("No state sequence has been defined. Use WithSequence() to define the execution order.");

            // Validate that all states in sequence have step configurations
            foreach (var state in _stateSequence)
            {
                if (!_stepConfigurations.Any(sc => EqualityComparer<TState>.Default.Equals(sc.State, state)))
                {
                    throw new InvalidOperationException($"State '{state}' is in the sequence but has no step configuration. Use AddStep() to configure it.");
                }
            }

            // Create the process
            var process = new MultiStepProcess<TState, TObject>(_configuration);

            // Configure all steps
            foreach (var stepConfig in _stepConfigurations)
            {
                if (stepConfig.StateTaskTimeout.HasValue && stepConfig.RollbackTaskTimeout.HasValue)
                {
                    process.ConfigureStep(
                        stepConfig.State,
                        stepConfig.StateTask,
                        stepConfig.RollbackTask,
                        stepConfig.StateTaskTimeout.Value,
                        stepConfig.RollbackTaskTimeout.Value,
                        stepConfig.Description);
                }
                else
                {
                    process.ConfigureStep(
                        stepConfig.State,
                        stepConfig.StateTask,
                        stepConfig.RollbackTask,
                        stepConfig.Description);
                }
            }

            // Define the sequence
            process.DefineSequence(_stateSequence);

            // Validate the process
            process.ValidateProcess();

            return process;
        }

        /// <summary>
        /// Builds the configured multi-step process with an initial object.
        /// </summary>
        /// <param name="statefulObject">The initial IStateful object</param>
        /// <param name="correlationId">Optional correlation ID</param>
        /// <returns>A new IMultiStepProcess instance</returns>
        public IMultiStepProcess<TState, TObject> Build(TObject statefulObject, string? correlationId = null)
        {
            var process = Build();
            process.Initialize(statefulObject, correlationId);
            return process;
        }
    }

    /// <summary>
    /// Internal class for holding step configuration during building.
    /// </summary>
    internal class StepConfiguration<TState, TObject>
        where TObject : IStateful<TState>
        where TState : notnull
    {
        public TState State { get; set; } = default(TState)!;
        public Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> StateTask { get; set; } = null!;
        public Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> RollbackTask { get; set; } = null!;
        public TimeSpan? StateTaskTimeout { get; set; }
        public TimeSpan? RollbackTaskTimeout { get; set; }
        public string? Description { get; set; }
    }
}
