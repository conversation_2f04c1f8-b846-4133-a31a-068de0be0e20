using shared.Components.MultiStepProcess;
using shared.Components.MultiStepProcess.Builders;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Examples
{
    /// <summary>
    /// Example demonstrating basic usage of the MultiStepProcess component.
    /// This example shows a simple order processing workflow with payment, inventory, and shipping steps.
    /// </summary>
    public class BasicUsageExample
    {
        // Example enum for order processing states
        public enum OrderProcessingState
        {
            Created,
            PaymentProcessing,
            InventoryReserved,
            Shipped,
            Completed
        }

        // Example order class that implements IStateful
        public class Order : IStateful<OrderProcessingState>
        {
            public string Id { get; set; } = Guid.NewGuid().ToString();
            public decimal Amount { get; set; }
            public string CustomerId { get; set; } = string.Empty;
            public OrderProcessingState Status { get; set; } = OrderProcessingState.Created;
            public List<string> ProcessingLog { get; set; } = new List<string>();
            public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
            public bool PaymentProcessed { get; set; } = false;
            public bool InventoryReserved { get; set; } = false;
            public bool Shipped { get; set; } = false;
        }

        /// <summary>
        /// Demonstrates basic process creation and execution.
        /// </summary>
        public static async Task<bool> RunBasicExample()
        {
            Console.WriteLine("=== MultiStepProcess Basic Example ===");

            // Create an order to process
            var order = new Order
            {
                Amount = 99.99m,
                CustomerId = "CUST123"
            };

            Console.WriteLine($"Processing order {order.Id} for customer {order.CustomerId}, amount: ${order.Amount}");

            // Create and configure the process using direct configuration
            var process = new MultiStepProcess<OrderProcessingState, Order>(order)
                .ConfigureStep(OrderProcessingState.Created, ProcessPaymentTask, RollbackPaymentTask, "Process payment")
                .ConfigureStep(OrderProcessingState.PaymentProcessing, ReserveInventoryTask, ReleaseInventoryTask, "Reserve inventory")
                .ConfigureStep(OrderProcessingState.InventoryReserved, ShipOrderTask, CancelShipmentTask, "Ship order")
                .ConfigureStep(OrderProcessingState.Shipped, CompleteOrderTask, RevertCompletionTask, "Complete order")
                .DefineSequence(
                    OrderProcessingState.Created,
                    OrderProcessingState.PaymentProcessing,
                    OrderProcessingState.InventoryReserved,
                    OrderProcessingState.Shipped,
                    OrderProcessingState.Completed);

            // Validate the process configuration
            try
            {
                process.ValidateProcess();
                Console.WriteLine("Process configuration validated successfully");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Process validation failed: {ex.Message}");
                return false;
            }

            // Execute the process iteration by iteration
            Console.WriteLine("\nExecuting process...");
            while (!process.IsFinished)
            {
                var (success, result) = await process.ExecuteIteration();

                Console.WriteLine($"Iteration result: Success={success}, Current State={process.CurrentState}, Process State={process.ProcessState}");
                
                if (result != null)
                {
                    Console.WriteLine($"Task result: {result}");
                }

                if (!success)
                {
                    Console.WriteLine($"Process failed: {process.FailureReason}");
                    if (process.LastException != null)
                    {
                        Console.WriteLine($"Last exception: {process.LastException.Message}");
                    }
                    break;
                }

                // Add a small delay to make the output more readable
                await Task.Delay(100);
            }

            // Display final results
            Console.WriteLine($"\nProcess finished. Success: {process.IsFinished && process.FailureReason == MultiStepProcessFailureReason.None}");
            Console.WriteLine($"Final state: {order.Status}");
            Console.WriteLine($"Processing log:");
            foreach (var logEntry in order.ProcessingLog)
            {
                Console.WriteLine($"  - {logEntry}");
            }

            return process.IsFinished && process.FailureReason == MultiStepProcessFailureReason.None;
        }

        /// <summary>
        /// Demonstrates using the builder pattern for process configuration.
        /// </summary>
        public static async Task<bool> RunBuilderExample()
        {
            Console.WriteLine("\n=== MultiStepProcess Builder Example ===");

            var order = new Order
            {
                Amount = 149.99m,
                CustomerId = "CUST456"
            };

            Console.WriteLine($"Processing order {order.Id} using builder pattern");

            // Create process using builder pattern with custom configuration
            var process = new MultiStepProcessBuilder<OrderProcessingState, Order>()
                .AddStep(OrderProcessingState.Created, ProcessPaymentTask, RollbackPaymentTask, "Process payment")
                .AddStep(OrderProcessingState.PaymentProcessing, ReserveInventoryTask, ReleaseInventoryTask, "Reserve inventory")
                .AddStep(OrderProcessingState.InventoryReserved, ShipOrderTask, CancelShipmentTask, "Ship order")
                .AddStep(OrderProcessingState.Shipped, CompleteOrderTask, RevertCompletionTask, "Complete order")
                .WithSequence(
                    OrderProcessingState.Created,
                    OrderProcessingState.PaymentProcessing,
                    OrderProcessingState.InventoryReserved,
                    OrderProcessingState.Shipped,
                    OrderProcessingState.Completed)
                .WithMaxRetries(2)
                .WithTimeoutBehavior(MultiStepProcessTaskRunResult.FailedRetry)
                .WithDefaultTimeout(TimeSpan.FromSeconds(30))
                .Build(order);

            // Execute to completion
            var (success, lastResult) = await process.ExecuteToCompletion();

            Console.WriteLine($"Process completed. Success: {success}");
            if (!success)
            {
                Console.WriteLine($"Failure reason: {process.FailureReason}");
            }

            return success;
        }

        /// <summary>
        /// Task for processing payment.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> ProcessPaymentTask(Order order)
        {
            Console.WriteLine($"  Processing payment for order {order.Id}...");
            
            // Simulate payment processing work
            await Task.Delay(200);

            // Simulate occasional failure for demonstration
            var random = new Random();
            if (random.NextDouble() < 0.1) // 10% chance of failure
            {
                order.ProcessingLog.Add("Payment processing failed - will retry");
                return (MultiStepProcessTaskRunResult.FailedRetry, "Payment gateway temporarily unavailable");
            }

            order.PaymentProcessed = true;
            order.ProcessingLog.Add($"Payment processed successfully: ${order.Amount}");
            return (MultiStepProcessTaskRunResult.Successful, $"Payment of ${order.Amount} processed");
        }

        /// <summary>
        /// Rollback task for payment processing.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> RollbackPaymentTask(Order order)
        {
            Console.WriteLine($"  Rolling back payment for order {order.Id}...");
            
            // Simulate payment refund work
            await Task.Delay(100);

            if (order.PaymentProcessed)
            {
                order.PaymentProcessed = false;
                order.ProcessingLog.Add($"Payment refunded: ${order.Amount}");
                return (MultiStepProcessTaskRunResult.Successful, $"Payment refunded: ${order.Amount}");
            }

            return (MultiStepProcessTaskRunResult.Successful, "No payment to refund");
        }

        /// <summary>
        /// Task for reserving inventory.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> ReserveInventoryTask(Order order)
        {
            Console.WriteLine($"  Reserving inventory for order {order.Id}...");
            
            // Simulate inventory reservation work
            await Task.Delay(150);

            order.InventoryReserved = true;
            order.ProcessingLog.Add("Inventory reserved successfully");
            return (MultiStepProcessTaskRunResult.Successful, "Inventory reserved");
        }

        /// <summary>
        /// Rollback task for inventory reservation.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> ReleaseInventoryTask(Order order)
        {
            Console.WriteLine($"  Releasing inventory for order {order.Id}...");
            
            // Simulate inventory release work
            await Task.Delay(50);

            if (order.InventoryReserved)
            {
                order.InventoryReserved = false;
                order.ProcessingLog.Add("Inventory released");
                return (MultiStepProcessTaskRunResult.Successful, "Inventory released");
            }

            return (MultiStepProcessTaskRunResult.Successful, "No inventory to release");
        }

        /// <summary>
        /// Task for shipping the order.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> ShipOrderTask(Order order)
        {
            Console.WriteLine($"  Shipping order {order.Id}...");
            
            // Simulate shipping work
            await Task.Delay(300);

            order.Shipped = true;
            order.ProcessingLog.Add("Order shipped successfully");
            return (MultiStepProcessTaskRunResult.Successful, "Order shipped");
        }

        /// <summary>
        /// Rollback task for shipping.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> CancelShipmentTask(Order order)
        {
            Console.WriteLine($"  Canceling shipment for order {order.Id}...");
            
            // Simulate shipment cancellation work
            await Task.Delay(100);

            if (order.Shipped)
            {
                order.Shipped = false;
                order.ProcessingLog.Add("Shipment canceled");
                return (MultiStepProcessTaskRunResult.Successful, "Shipment canceled");
            }

            return (MultiStepProcessTaskRunResult.Successful, "No shipment to cancel");
        }

        /// <summary>
        /// Task for completing the order.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> CompleteOrderTask(Order order)
        {
            Console.WriteLine($"  Completing order {order.Id}...");
            
            // Simulate order completion work
            await Task.Delay(100);

            order.ProcessingLog.Add("Order completed successfully");
            return (MultiStepProcessTaskRunResult.Successful, $"Order {order.Id} completed");
        }

        /// <summary>
        /// Rollback task for order completion.
        /// </summary>
        private static async Task<(MultiStepProcessTaskRunResult, object?)> RevertCompletionTask(Order order)
        {
            Console.WriteLine($"  Reverting completion for order {order.Id}...");
            
            // Simulate completion reversion work
            await Task.Delay(50);

            order.ProcessingLog.Add("Order completion reverted");
            return (MultiStepProcessTaskRunResult.Successful, "Order completion reverted");
        }
    }
}
