using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Interfaces
{
    /// <summary>
    /// Interface for a multi-step process that can execute forward and rollback operations.
    /// Provides a double-linked list builder and runner for executing multiple steps in order
    /// with the ability to revert execution if the process needs to be aborted.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public interface IMultiStepProcess<TState, TObject> 
        where TObject : IStateful<TState>
        where TState : notnull
    {
        /// <summary>
        /// Gets the current state data of the multi-step process.
        /// </summary>
        MultiStepProcessStateData<TState, TObject> StateData { get; }

        /// <summary>
        /// Gets the current state of the IStateful object.
        /// </summary>
        TState CurrentState { get; }

        /// <summary>
        /// Gets the current process state (Running or RollingBack).
        /// </summary>
        MultiStepProcessState ProcessState { get; }

        /// <summary>
        /// Gets whether the process has finished processing.
        /// </summary>
        bool IsFinished { get; }

        /// <summary>
        /// Gets the current retry count for the current step.
        /// </summary>
        int RetryCount { get; }

        /// <summary>
        /// Gets the reason for process failure, if any.
        /// </summary>
        MultiStepProcessFailureReason FailureReason { get; }

        /// <summary>
        /// Gets the process configuration.
        /// </summary>
        MultiStepProcessConfiguration Configuration { get; }

        /// <summary>
        /// Gets whether the last iteration failed.
        /// </summary>
        bool LastIterationFailed { get; }

        /// <summary>
        /// Gets the last exception that occurred, if any.
        /// </summary>
        Exception? LastException { get; }

        /// <summary>
        /// Configures a step in the process with both StateTask and RollbackTask.
        /// </summary>
        /// <param name="state">The state this step applies to</param>
        /// <param name="stateTask">Task to execute when moving forward</param>
        /// <param name="rollbackTask">Task to execute when rolling back</param>
        /// <param name="description">Optional description of the step</param>
        /// <returns>This process instance for method chaining</returns>
        IMultiStepProcess<TState, TObject> ConfigureStep(
            TState state,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> stateTask,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> rollbackTask,
            string? description = null);

        /// <summary>
        /// Configures a step with timeouts for both StateTask and RollbackTask.
        /// </summary>
        /// <param name="state">The state this step applies to</param>
        /// <param name="stateTask">Task to execute when moving forward</param>
        /// <param name="rollbackTask">Task to execute when rolling back</param>
        /// <param name="stateTaskTimeout">Timeout for the StateTask</param>
        /// <param name="rollbackTaskTimeout">Timeout for the RollbackTask</param>
        /// <param name="description">Optional description of the step</param>
        /// <returns>This process instance for method chaining</returns>
        IMultiStepProcess<TState, TObject> ConfigureStep(
            TState state,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> stateTask,
            Func<TObject, Task<(MultiStepProcessTaskRunResult, object?)>> rollbackTask,
            TimeSpan stateTaskTimeout,
            TimeSpan rollbackTaskTimeout,
            string? description = null);

        /// <summary>
        /// Defines the sequence of states by linking them together.
        /// Creates the double-linked list structure for forward and backward navigation.
        /// </summary>
        /// <param name="stateSequence">Array of states in execution order</param>
        /// <returns>This process instance for method chaining</returns>
        IMultiStepProcess<TState, TObject> DefineSequence(params TState[] stateSequence);

        /// <summary>
        /// Initializes the process with a TObject instance.
        /// </summary>
        /// <param name="statefulObject">The IStateful object to process</param>
        /// <param name="correlationId">Optional correlation ID for tracking</param>
        void Initialize(TObject statefulObject, string? correlationId = null);

        /// <summary>
        /// Loads the complete state data into the process.
        /// This allows resuming from a previously saved state.
        /// </summary>
        /// <param name="stateData">The state data to load</param>
        void LoadStateData(MultiStepProcessStateData<TState, TObject> stateData);

        /// <summary>
        /// Sets the current state of the process and the IStateful object.
        /// This allows loading state from external configuration or resuming from a saved state.
        /// </summary>
        /// <param name="state">The state to set</param>
        /// <param name="processState">The process state to set</param>
        void SetCurrentState(TState state, MultiStepProcessState processState = MultiStepProcessState.Running);

        /// <summary>
        /// Executes one complete iteration of the process.
        /// Returns a tuple indicating success and any result object from the task.
        /// </summary>
        /// <returns>Tuple of (success, result object)</returns>
        Task<(bool success, object? result)> ExecuteIteration();

        /// <summary>
        /// Executes the process until it reaches a finished state or fails.
        /// </summary>
        /// <param name="maxIterations">Maximum number of iterations to prevent infinite loops</param>
        /// <returns>Tuple of (success, last result object)</returns>
        Task<(bool success, object? lastResult)> ExecuteToCompletion(int maxIterations = 100);

        /// <summary>
        /// Validates that the process configuration is valid and complete.
        /// </summary>
        /// <returns>True if the process is valid</returns>
        /// <exception cref="InvalidOperationException">Thrown when process configuration is invalid</exception>
        bool ValidateProcess();

        /// <summary>
        /// Forces the process to start rolling back from the current state.
        /// </summary>
        void StartRollback();

        /// <summary>
        /// Gets detailed information about the current process state for debugging.
        /// </summary>
        /// <returns>Dictionary containing process state information</returns>
        Dictionary<string, object> GetProcessInfo();

        /// <summary>
        /// Checks if the process can execute another iteration.
        /// </summary>
        /// <returns>True if the process can continue</returns>
        bool CanExecute();
    }
}
