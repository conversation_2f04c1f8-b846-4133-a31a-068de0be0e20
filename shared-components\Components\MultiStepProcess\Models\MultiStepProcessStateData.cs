using Amazon.DynamoDBv2.DataModel;
using shared.Components.MultiStepProcess.Enums;
using shared.Converters;
using shared.Models.Interfaces;
using System.Text.Json.Serialization;

namespace shared.Components.MultiStepProcess.Models
{
    /// <summary>
    /// Represents the complete state data for a multi-step process.
    /// This class is serializable and can be easily saved/loaded for persistence.
    /// </summary>
    /// <typeparam name="TState">The type of state used by the IStateful object</typeparam>
    /// <typeparam name="TObject">The type of the payload object that implements IStateful</typeparam>
    public class MultiStepProcessStateData<TState, TObject> 
        where TObject : IStateful<TState>
        where TState : notnull
    {
        /// <summary>
        /// The current process state that controls execution flow (Running or RollingBack).
        /// </summary>
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<MultiStepProcessState>))]
        [JsonConverter(typeof(JsonEnumStringConverter<MultiStepProcessState>))]
        public MultiStepProcessState ProcessState { get; set; } = MultiStepProcessState.Running;

        /// <summary>
        /// The IStateful object that tracks the actual business state.
        /// This object's Status property will be updated during state transitions.
        /// </summary>
        [JsonConverter(typeof(JsonTypedConverter<object>))]
        public TObject StatefulObject { get; set; }

        /// <summary>
        /// The current retry count for the current state.
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// Optional correlation ID for tracking and debugging.
        /// </summary>
        public string? CorrelationId { get; set; }

        /// <summary>
        /// Timestamp when the state data was created.
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Timestamp when the state data was last updated.
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Whether the process has finished (successfully or with failure).
        /// </summary>
        public bool IsFinished { get; set; } = false;

        /// <summary>
        /// The reason for process failure, if any.
        /// </summary>
        [DynamoDBProperty(typeof(DynamoEnumStringConverter<MultiStepProcessFailureReason>))]
        [JsonConverter(typeof(JsonEnumStringConverter<MultiStepProcessFailureReason>))]
        public MultiStepProcessFailureReason FailureReason { get; set; } = MultiStepProcessFailureReason.None;

        /// <summary>
        /// The current state of the state machine (from the IStateful object).
        /// </summary>
        [JsonIgnore]
        public TState CurrentState => StatefulObject.Status;

        /// <summary>
        /// The payload data for the process.
        /// This is the same object as StatefulObject but provides a generic interface.
        /// </summary>
        [JsonIgnore]
        public TObject Payload => StatefulObject;

        /// <summary>
        /// Creates a new instance of MultiStepProcessStateData.
        /// </summary>
        /// <param name="statefulObject">The IStateful object to track</param>
        /// <param name="correlationId">Optional correlation ID</param>
        public MultiStepProcessStateData(TObject statefulObject, string? correlationId = null)
        {
            StatefulObject = statefulObject ?? throw new ArgumentNullException(nameof(statefulObject));
            CorrelationId = correlationId;
        }

        /// <summary>
        /// Parameterless constructor for serialization.
        /// </summary>
        public MultiStepProcessStateData()
        {
            StatefulObject = default(TObject)!;
        }

        /// <summary>
        /// Updates the process state and timestamp.
        /// </summary>
        /// <param name="processState">New process state</param>
        public void UpdateProcessState(MultiStepProcessState processState)
        {
            ProcessState = processState;
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Increments the retry count and updates timestamp.
        /// </summary>
        public void IncrementRetryCount()
        {
            RetryCount++;
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Resets the retry count and updates timestamp.
        /// </summary>
        public void ResetRetryCount()
        {
            RetryCount = 0;
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Marks the process as finished with optional failure reason.
        /// </summary>
        /// <param name="failureReason">Reason for failure, if any</param>
        public void MarkAsFinished(MultiStepProcessFailureReason failureReason = MultiStepProcessFailureReason.None)
        {
            IsFinished = true;
            FailureReason = failureReason;
            UpdatedAt = DateTime.UtcNow;
        }

        /// <summary>
        /// Checks if the retry limit would be exceeded for the given max retries.
        /// </summary>
        /// <param name="maxRetries">Maximum allowed retries</param>
        /// <returns>True if retry limit would be exceeded</returns>
        public bool IsRetryLimitExceeded(int maxRetries)
        {
            return RetryCount >= maxRetries;
        }
    }
}
