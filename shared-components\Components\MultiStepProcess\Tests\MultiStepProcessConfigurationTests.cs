using Microsoft.VisualStudio.TestTools.UnitTesting;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Models;

namespace shared.Components.MultiStepProcess.Tests
{
    [TestClass]
    public class MultiStepProcessConfigurationTests
    {
        [TestMethod]
        public void Constructor_WithDefaults_ShouldSetDefaultValues()
        {
            // Act
            var config = new MultiStepProcessConfiguration();

            // Assert
            Assert.AreEqual(3, config.MaxRetries);
            Assert.AreEqual(MultiStepProcessTaskRunResult.FailedRetry, config.TimeoutBehavior);
            Assert.IsNull(config.DefaultTaskTimeout);
            Assert.IsTrue(config.ResetRetryCountOnStateTransition);
            Assert.IsTrue(config.ValidateConfigurationOnInit);
        }

        [TestMethod]
        public void Constructor_WithParameters_ShouldSetValues()
        {
            // Arrange
            var timeout = TimeSpan.FromSeconds(30);

            // Act
            var config = new MultiStepProcessConfiguration(
                maxRetries: 5,
                timeoutBehavior: MultiStepProcessTaskRunResult.FailedAbort,
                defaultTaskTimeout: timeout);

            // Assert
            Assert.AreEqual(5, config.MaxRetries);
            Assert.AreEqual(MultiStepProcessTaskRunResult.FailedAbort, config.TimeoutBehavior);
            Assert.AreEqual(timeout, config.DefaultTaskTimeout);
        }

        [TestMethod]
        public void Validate_WithValidConfiguration_ShouldReturnTrue()
        {
            // Arrange
            var config = new MultiStepProcessConfiguration
            {
                MaxRetries = 3,
                TimeoutBehavior = MultiStepProcessTaskRunResult.FailedRetry,
                DefaultTaskTimeout = TimeSpan.FromSeconds(30)
            };

            // Act & Assert
            Assert.IsTrue(config.Validate());
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void Validate_WithNegativeMaxRetries_ShouldThrowException()
        {
            // Arrange
            var config = new MultiStepProcessConfiguration
            {
                MaxRetries = -1
            };

            // Act
            config.Validate();
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void Validate_WithInvalidTimeoutBehavior_ShouldThrowException()
        {
            // Arrange
            var config = new MultiStepProcessConfiguration
            {
                TimeoutBehavior = MultiStepProcessTaskRunResult.Successful // Invalid for timeout behavior
            };

            // Act
            config.Validate();
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void Validate_WithZeroDefaultTimeout_ShouldThrowException()
        {
            // Arrange
            var config = new MultiStepProcessConfiguration
            {
                DefaultTaskTimeout = TimeSpan.Zero
            };

            // Act
            config.Validate();
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentException))]
        public void Validate_WithNegativeDefaultTimeout_ShouldThrowException()
        {
            // Arrange
            var config = new MultiStepProcessConfiguration
            {
                DefaultTaskTimeout = TimeSpan.FromSeconds(-1)
            };

            // Act
            config.Validate();
        }

        [TestMethod]
        public void Clone_ShouldCreateIdenticalCopy()
        {
            // Arrange
            var original = new MultiStepProcessConfiguration
            {
                MaxRetries = 5,
                TimeoutBehavior = MultiStepProcessTaskRunResult.FailedAbort,
                DefaultTaskTimeout = TimeSpan.FromSeconds(60),
                ResetRetryCountOnStateTransition = false,
                ValidateConfigurationOnInit = false
            };

            // Act
            var clone = original.Clone();

            // Assert
            Assert.AreNotSame(original, clone);
            Assert.AreEqual(original.MaxRetries, clone.MaxRetries);
            Assert.AreEqual(original.TimeoutBehavior, clone.TimeoutBehavior);
            Assert.AreEqual(original.DefaultTaskTimeout, clone.DefaultTaskTimeout);
            Assert.AreEqual(original.ResetRetryCountOnStateTransition, clone.ResetRetryCountOnStateTransition);
            Assert.AreEqual(original.ValidateConfigurationOnInit, clone.ValidateConfigurationOnInit);
        }

        [TestMethod]
        public void Clone_ModifyingClone_ShouldNotAffectOriginal()
        {
            // Arrange
            var original = new MultiStepProcessConfiguration { MaxRetries = 3 };
            var clone = original.Clone();

            // Act
            clone.MaxRetries = 10;

            // Assert
            Assert.AreEqual(3, original.MaxRetries);
            Assert.AreEqual(10, clone.MaxRetries);
        }
    }
}
