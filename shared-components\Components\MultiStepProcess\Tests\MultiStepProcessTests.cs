using Microsoft.VisualStudio.TestTools.UnitTesting;
using shared.Components.MultiStepProcess;
using shared.Components.MultiStepProcess.Builders;
using shared.Components.MultiStepProcess.Enums;
using shared.Components.MultiStepProcess.Exceptions;
using shared.Components.MultiStepProcess.Models;
using shared.Models.Interfaces;

namespace shared.Components.MultiStepProcess.Tests
{
    [TestClass]
    public class MultiStepProcessTests
    {
        // Test state enum
        public enum TestState
        {
            Initial,
            Processing,
            Completed
        }

        // Test object implementing IStateful
        public class TestObject : IStateful<TestState>
        {
            public string Id { get; set; } = Guid.NewGuid().ToString();
            public TestState Status { get; set; } = TestState.Initial;
            public List<string> Log { get; set; } = new List<string>();
            public bool ShouldFail { get; set; } = false;
            public bool ShouldTimeout { get; set; } = false;
        }

        [TestMethod]
        public void Constructor_WithDefaultConfiguration_ShouldCreateProcess()
        {
            // Arrange & Act
            var process = new MultiStepProcess<TestState, TestObject>();

            // Assert
            Assert.IsNotNull(process);
            Assert.IsNotNull(process.Configuration);
            Assert.AreEqual(3, process.Configuration.MaxRetries);
        }

        [TestMethod]
        public void Constructor_WithCustomConfiguration_ShouldUseConfiguration()
        {
            // Arrange
            var config = new MultiStepProcessConfiguration
            {
                MaxRetries = 5,
                TimeoutBehavior = MultiStepProcessTaskRunResult.FailedAbort
            };

            // Act
            var process = new MultiStepProcess<TestState, TestObject>(config);

            // Assert
            Assert.AreEqual(5, process.Configuration.MaxRetries);
            Assert.AreEqual(MultiStepProcessTaskRunResult.FailedAbort, process.Configuration.TimeoutBehavior);
        }

        [TestMethod]
        public void Initialize_WithValidObject_ShouldSetStateData()
        {
            // Arrange
            var process = new MultiStepProcess<TestState, TestObject>();
            var testObject = new TestObject();

            // Act
            process.Initialize(testObject, "test-correlation");

            // Assert
            Assert.AreEqual(testObject.Status, process.CurrentState);
            Assert.AreEqual(MultiStepProcessState.Running, process.ProcessState);
            Assert.AreEqual("test-correlation", process.StateData.CorrelationId);
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void Initialize_WithNullObject_ShouldThrowException()
        {
            // Arrange
            var process = new MultiStepProcess<TestState, TestObject>();

            // Act
            process.Initialize(null);
        }

        [TestMethod]
        public void ConfigureStep_WithValidParameters_ShouldAddStep()
        {
            // Arrange
            var process = new MultiStepProcess<TestState, TestObject>();

            // Act
            var result = process.ConfigureStep(
                TestState.Initial,
                SuccessfulStateTask,
                SuccessfulRollbackTask,
                "Test step");

            // Assert
            Assert.AreSame(process, result); // Should return same instance for chaining
        }

        [TestMethod]
        [ExpectedException(typeof(ArgumentNullException))]
        public void ConfigureStep_WithNullStateTask_ShouldThrowException()
        {
            // Arrange
            var process = new MultiStepProcess<TestState, TestObject>();

            // Act
            process.ConfigureStep(TestState.Initial, null, SuccessfulRollbackTask);
        }

        [TestMethod]
        public void DefineSequence_WithValidStates_ShouldLinkStates()
        {
            // Arrange
            var process = new MultiStepProcess<TestState, TestObject>();
            process.ConfigureStep(TestState.Initial, SuccessfulStateTask, SuccessfulRollbackTask);
            process.ConfigureStep(TestState.Processing, SuccessfulStateTask, SuccessfulRollbackTask);
            process.ConfigureStep(TestState.Completed, SuccessfulStateTask, SuccessfulRollbackTask);

            // Act
            process.DefineSequence(TestState.Initial, TestState.Processing, TestState.Completed);

            // Assert
            Assert.IsTrue(process.ValidateProcess());
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void DefineSequence_WithUnconfiguredState_ShouldThrowException()
        {
            // Arrange
            var process = new MultiStepProcess<TestState, TestObject>();
            process.ConfigureStep(TestState.Initial, SuccessfulStateTask, SuccessfulRollbackTask);

            // Act
            process.DefineSequence(TestState.Initial, TestState.Processing); // Processing not configured
        }

        [TestMethod]
        public async Task ExecuteIteration_WithSuccessfulTask_ShouldTransitionState()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Act
            var (success, result) = await process.ExecuteIteration();

            // Assert
            Assert.IsTrue(success);
            Assert.AreEqual(TestState.Processing, process.CurrentState);
            Assert.AreEqual(MultiStepProcessState.Running, process.ProcessState);
        }

        [TestMethod]
        public async Task ExecuteIteration_WithRetryableFailure_ShouldIncrementRetryCount()
        {
            // Arrange
            var testObject = new TestObject { ShouldFail = true };
            var process = CreateBasicProcess(testObject);

            // Act
            var (success, result) = await process.ExecuteIteration();

            // Assert
            Assert.IsTrue(success); // Retry is considered successful iteration
            Assert.AreEqual(1, process.RetryCount);
            Assert.AreEqual(TestState.Initial, process.CurrentState); // Should stay in same state
        }

        [TestMethod]
        public async Task ExecuteIteration_WithAbortFailure_ShouldStartRollback()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Configure a task that aborts
            process.ConfigureStep(TestState.Initial, AbortStateTask, SuccessfulRollbackTask);

            // Act
            var (success, result) = await process.ExecuteIteration();

            // Assert
            Assert.IsTrue(success);
            Assert.AreEqual(MultiStepProcessState.RollingBack, process.ProcessState);
        }

        [TestMethod]
        public async Task ExecuteToCompletion_WithSuccessfulProcess_ShouldCompleteSuccessfully()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Act
            var (success, lastResult) = await process.ExecuteToCompletion();

            // Assert
            Assert.IsTrue(success);
            Assert.IsTrue(process.IsFinished);
            Assert.AreEqual(MultiStepProcessFailureReason.None, process.FailureReason);
        }

        [TestMethod]
        public void ValidateProcess_WithValidConfiguration_ShouldReturnTrue()
        {
            // Arrange
            var process = CreateBasicProcess(new TestObject());

            // Act & Assert
            Assert.IsTrue(process.ValidateProcess());
        }

        [TestMethod]
        [ExpectedException(typeof(ProcessConfigurationException))]
        public void ValidateProcess_WithNoSteps_ShouldThrowException()
        {
            // Arrange
            var process = new MultiStepProcess<TestState, TestObject>();

            // Act
            process.ValidateProcess();
        }

        [TestMethod]
        public void StartRollback_WhenRunning_ShouldSwitchToRollback()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Act
            process.StartRollback();

            // Assert
            Assert.AreEqual(MultiStepProcessState.RollingBack, process.ProcessState);
            Assert.AreEqual(0, process.RetryCount); // Should reset retry count
        }

        [TestMethod]
        public void GetProcessInfo_ShouldReturnDetailedInformation()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Act
            var info = process.GetProcessInfo();

            // Assert
            Assert.IsTrue(info.ContainsKey("CurrentState"));
            Assert.IsTrue(info.ContainsKey("ProcessState"));
            Assert.IsTrue(info.ContainsKey("IsFinished"));
            Assert.IsTrue(info.ContainsKey("MaxRetries"));
        }

        [TestMethod]
        public void Builder_WithValidConfiguration_ShouldCreateProcess()
        {
            // Arrange
            var testObject = new TestObject();

            // Act
            var process = new MultiStepProcessBuilder<TestState, TestObject>()
                .AddStep(TestState.Initial, SuccessfulStateTask, SuccessfulRollbackTask)
                .AddStep(TestState.Processing, SuccessfulStateTask, SuccessfulRollbackTask)
                .AddStep(TestState.Completed, SuccessfulStateTask, SuccessfulRollbackTask)
                .WithSequence(TestState.Initial, TestState.Processing, TestState.Completed)
                .WithMaxRetries(5)
                .Build(testObject);

            // Assert
            Assert.IsNotNull(process);
            Assert.AreEqual(5, process.Configuration.MaxRetries);
            Assert.AreEqual(testObject.Status, process.CurrentState);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public void Builder_WithNoSteps_ShouldThrowException()
        {
            // Act
            new MultiStepProcessBuilder<TestState, TestObject>()
                .WithSequence(TestState.Initial)
                .Build();
        }

        [TestMethod]
        public void StateData_Serialization_ShouldPreserveData()
        {
            // Arrange
            var testObject = new TestObject { Id = "test-123" };
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject, "correlation-123");
            stateData.IncrementRetryCount();
            stateData.UpdateProcessState(MultiStepProcessState.RollingBack);

            // Act
            var json = System.Text.Json.JsonSerializer.Serialize(stateData);
            var deserializedStateData = System.Text.Json.JsonSerializer.Deserialize<MultiStepProcessStateData<TestState, TestObject>>(json);

            // Assert
            Assert.IsNotNull(deserializedStateData);
            Assert.AreEqual("test-123", deserializedStateData.StatefulObject.Id);
            Assert.AreEqual("correlation-123", deserializedStateData.CorrelationId);
            Assert.AreEqual(1, deserializedStateData.RetryCount);
            Assert.AreEqual(MultiStepProcessState.RollingBack, deserializedStateData.ProcessState);
        }

        // Helper methods for creating test tasks
        private static async Task<(MultiStepProcessTaskRunResult, object?)> SuccessfulStateTask(TestObject obj)
        {
            await Task.Delay(10);
            obj.Log.Add($"StateTask executed for {obj.Status}");
            return (MultiStepProcessTaskRunResult.Successful, $"StateTask completed for {obj.Status}");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> SuccessfulRollbackTask(TestObject obj)
        {
            await Task.Delay(10);
            obj.Log.Add($"RollbackTask executed for {obj.Status}");
            return (MultiStepProcessTaskRunResult.Successful, $"RollbackTask completed for {obj.Status}");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> RetryableFailureTask(TestObject obj)
        {
            await Task.Delay(10);
            if (obj.ShouldFail)
            {
                obj.Log.Add($"Task failed for {obj.Status} - will retry");
                return (MultiStepProcessTaskRunResult.FailedRetry, "Temporary failure");
            }
            return (MultiStepProcessTaskRunResult.Successful, "Success after retry");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> AbortStateTask(TestObject obj)
        {
            await Task.Delay(10);
            obj.Log.Add($"Task aborted for {obj.Status}");
            return (MultiStepProcessTaskRunResult.FailedAbort, "Task aborted");
        }

        [TestMethod]
        public async Task ExecuteIteration_WithTimeoutTask_ShouldHandleTimeout()
        {
            // Arrange
            var testObject = new TestObject();
            var config = new MultiStepProcessConfiguration
            {
                TimeoutBehavior = MultiStepProcessTaskRunResult.FailedRetry,
                DefaultTaskTimeout = TimeSpan.FromMilliseconds(50)
            };
            var process = new MultiStepProcess<TestState, TestObject>(testObject, config);

            process.ConfigureStep(TestState.Initial, TimeoutTask, SuccessfulRollbackTask);
            process.ConfigureStep(TestState.Processing, SuccessfulStateTask, SuccessfulRollbackTask);
            process.ConfigureStep(TestState.Completed, SuccessfulStateTask, SuccessfulRollbackTask);
            process.DefineSequence(TestState.Initial, TestState.Processing, TestState.Completed);

            // Act
            var (success, result) = await process.ExecuteIteration();

            // Assert
            Assert.IsTrue(success); // Timeout treated as retry
            Assert.AreEqual(1, process.RetryCount);
        }

        [TestMethod]
        public async Task ExecuteIteration_WithRetryLimitExceeded_ShouldStartRollback()
        {
            // Arrange
            var testObject = new TestObject { ShouldFail = true };
            var config = new MultiStepProcessConfiguration { MaxRetries = 1 };
            var process = new MultiStepProcess<TestState, TestObject>(testObject, config);

            process.ConfigureStep(TestState.Initial, RetryableFailureTask, SuccessfulRollbackTask);
            process.ConfigureStep(TestState.Processing, SuccessfulStateTask, SuccessfulRollbackTask);
            process.ConfigureStep(TestState.Completed, SuccessfulStateTask, SuccessfulRollbackTask);
            process.DefineSequence(TestState.Initial, TestState.Processing, TestState.Completed);

            // Act - First iteration should retry
            var (success1, result1) = await process.ExecuteIteration();
            Assert.IsTrue(success1);
            Assert.AreEqual(1, process.RetryCount);

            // Act - Second iteration should exceed retry limit and start rollback
            var (success2, result2) = await process.ExecuteIteration();

            // Assert
            Assert.IsTrue(success2);
            Assert.AreEqual(MultiStepProcessState.RollingBack, process.ProcessState);
            Assert.IsNotNull(process.LastException);
            Assert.IsInstanceOfType(process.LastException, typeof(MaxRetryExceededException));
        }

        [TestMethod]
        public async Task ExecuteIteration_InRollbackWithAbort_ShouldFinishWithFailure()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Start rollback
            process.StartRollback();

            // Configure rollback task to abort
            process.ConfigureStep(TestState.Initial, SuccessfulStateTask, AbortRollbackTask);

            // Act
            var (success, result) = await process.ExecuteIteration();

            // Assert
            Assert.IsFalse(success);
            Assert.IsTrue(process.IsFinished);
            Assert.AreEqual(MultiStepProcessFailureReason.RollbackFailed, process.FailureReason);
        }

        [TestMethod]
        [ExpectedException(typeof(InvalidOperationException))]
        public async Task ExecuteIteration_WhenFinished_ShouldThrowException()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Manually mark as finished
            process.StateData.MarkAsFinished();

            // Act
            await process.ExecuteIteration();
        }

        [TestMethod]
        public void CanExecute_WhenNotFinished_ShouldReturnTrue()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Act & Assert
            Assert.IsTrue(process.CanExecute());
        }

        [TestMethod]
        public void CanExecute_WhenFinished_ShouldReturnFalse()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);
            process.StateData.MarkAsFinished();

            // Act & Assert
            Assert.IsFalse(process.CanExecute());
        }

        [TestMethod]
        public void LoadStateData_WithValidData_ShouldRestoreState()
        {
            // Arrange
            var testObject = new TestObject { Status = TestState.Processing };
            var stateData = new MultiStepProcessStateData<TestState, TestObject>(testObject, "test-correlation");
            stateData.IncrementRetryCount();
            stateData.UpdateProcessState(MultiStepProcessState.RollingBack);

            var process = new MultiStepProcess<TestState, TestObject>();
            CreateBasicProcessConfiguration(process);

            // Act
            process.LoadStateData(stateData);

            // Assert
            Assert.AreEqual(TestState.Processing, process.CurrentState);
            Assert.AreEqual(MultiStepProcessState.RollingBack, process.ProcessState);
            Assert.AreEqual(1, process.RetryCount);
            Assert.AreEqual("test-correlation", process.StateData.CorrelationId);
        }

        [TestMethod]
        public void SetCurrentState_WithValidState_ShouldUpdateState()
        {
            // Arrange
            var testObject = new TestObject();
            var process = CreateBasicProcess(testObject);

            // Act
            process.SetCurrentState(TestState.Processing, MultiStepProcessState.RollingBack);

            // Assert
            Assert.AreEqual(TestState.Processing, process.CurrentState);
            Assert.AreEqual(MultiStepProcessState.RollingBack, process.ProcessState);
            Assert.AreEqual(0, process.RetryCount); // Should reset retry count
        }

        // Additional helper methods
        private static async Task<(MultiStepProcessTaskRunResult, object?)> TimeoutTask(TestObject obj)
        {
            await Task.Delay(200); // Longer than the configured timeout
            return (MultiStepProcessTaskRunResult.Successful, "Should not reach here");
        }

        private static async Task<(MultiStepProcessTaskRunResult, object?)> AbortRollbackTask(TestObject obj)
        {
            await Task.Delay(10);
            obj.Log.Add($"RollbackTask aborted for {obj.Status}");
            return (MultiStepProcessTaskRunResult.FailedAbort, "Rollback aborted");
        }

        private void CreateBasicProcessConfiguration(MultiStepProcess<TestState, TestObject> process)
        {
            process.ConfigureStep(TestState.Initial, SuccessfulStateTask, SuccessfulRollbackTask, "Initial step");
            process.ConfigureStep(TestState.Processing, SuccessfulStateTask, SuccessfulRollbackTask, "Processing step");
            process.ConfigureStep(TestState.Completed, SuccessfulStateTask, SuccessfulRollbackTask, "Completed step");
            process.DefineSequence(TestState.Initial, TestState.Processing, TestState.Completed);
        }

        private MultiStepProcess<TestState, TestObject> CreateBasicProcess(TestObject testObject)
        {
            var process = new MultiStepProcess<TestState, TestObject>(testObject);
            CreateBasicProcessConfiguration(process);
            return process;
        }
    }
}
